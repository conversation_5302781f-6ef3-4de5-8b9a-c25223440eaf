import { Hono } from "hono";
import { verifySupabaseAuth } from "../middleware/auth.middleware";
import { 
  getActionforSlack,
  getUserIntegrationCredentials,
  addSlackConnection,
  handleSlackCallback,
  getChannels,
  getUsers,
  linkFormToSlack,
  updateSlackConnection
} from "../controllers/slack.controllers";

const slack = new Hono();

// Get actions for Slack integration
slack.get("/action/:id", verifySupabaseAuth, getActionforSlack);

// Get user's Slack credentials
slack.get("/connection/:id", verifySupabaseAuth, getUserIntegrationCredentials);

// OAuth flow - initiate Slack connection
slack.get('/addconnection',verifySupabaseAuth,addSlackConnection);

// OAuth callback - handle Slack authorization
slack.get('/callback', handleSlackCallback);

// Get Slack channels
slack.post("/channels", verifySupabaseAuth, getChannels);

// Get Slack users
slack.post("/users", verifySupabaseA<PERSON>, getUsers);

// Link form to Slack
slack.post("/linkform", verifySupabaseAuth, linkFormToSlack);

// Update Slack connection
slack.put('/updateconnection', verifySupabaseAuth, updateSlackConnection);

export { slack };
