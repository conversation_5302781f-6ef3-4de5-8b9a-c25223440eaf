import { Hono } from "hono";
import { verifySupabaseAuth } from "../middleware/auth.middleware";
import {
  getActionforOutlook,
  getUserIntegrationCredentials,
  addOutlookConnection,
  getProfile,
  testEmail,
  linkFormToOutlook,
  handleOutlookCallback
} from "../controllers/outlook.controllers";

const outlook = new Hono();

// Get actions for Outlook integration
outlook.get("/action/:id", verifySupabaseAuth, getActionforOutlook);

// Get user's Outlook credentials
outlook.get("/connection/:id", verifySupabaseAuth, getUserIntegrationCredentials);

// OAuth flow - initiate Outlook connection
outlook.get('/addconnection',verifySupabaseAuth, addOutlookConnection);

// OAuth callback - handle Outlook authorization
outlook.get('/callback', handleOutlookCallback);

// Get Outlook profile
outlook.post("/profile", verifySupabaseAuth, getProfile);

// Test email sending
outlook.post("/test", verifySupabaseAuth, testEmail);

// Link form to Outlook
outlook.post("/linkform", verifySupabaseAuth, linkFormToOutlook);

export { outlook };
