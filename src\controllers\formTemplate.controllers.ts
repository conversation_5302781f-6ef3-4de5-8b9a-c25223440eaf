
import type { Context } from "hono";
import { supabase } from "../db";
import { sendApiError, sendApiResponse } from "../utils/Response";
interface TemplateData {
  id: string;
  name: string;
  description: string;
  template_data: object;
  image_url: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  category_id: string;
}

const getCategories = async (c: Context) => {
  try {

    const { data: categories, error: categoriesError } = await supabase
      .from("automate_form_temp_categories")
      .select("id, name, description, icon");

    if (categoriesError) {
      console.error("Get Categories Error:", categoriesError);
      return sendApiError(c, "Failed to retrieve categories", 500);
    }

    if (!categories || categories.length === 0) {
      return sendApiError(c, "No categories found", 404);
    }

    return sendApiResponse(c, { categories }, 200);
  } catch (err) {
    console.error("Get Categories Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};


const createCategory = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const userId = user.user.id;
    const body = await c.req.json();

    if (!body.name) {
      return sendApiError(c, "Category name is required", 400);
    }

    // Fetch the user's role
    const { data: userProfile, error: userProfileError } = await supabase
      .from("user_profile")
      .select("role")
      .eq("id", userId)
      .single();

    if (userProfileError) {
      console.error("Get User Profile Error:", userProfileError);
      return sendApiError(c, "Failed to retrieve user profile", 500);
    }

    // Check if user is admin
    if (userProfile.role !== "admin") {
      return sendApiError(c, "Forbidden: Only admins can create categories", 403);
    }

    const newCategory = {
      name: body.name,
      description: body.description || null,
      icon: body.icon || null
    };

    // Insert into database
    const { data, error } = await supabase
      .from("automate_form_temp_categories")
      .insert([newCategory])
      .select("id, name, description, icon, created_at, updated_at");

    if (error) {
      console.error("Create Category Error:", error);
      return sendApiError(c, "Failed to create category", 500);
    }

    return sendApiResponse(c, { category: data[0] }, 201);
  } catch (err) {
    console.error("Create Category Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

const updateCategory = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const userId = user.user.id;
    const categoryId = c.req.param("id");
    const body = await c.req.json();

    if (!categoryId) {
      return sendApiError(c, "Category ID is required", 400);
    }

    // Get user role
    const { data: userProfile, error: userProfileError } = await supabase
      .from("user_profile")
      .select("role")
      .eq("id", userId)
      .single();

    if (userProfileError || !userProfile) {
      console.error("Get User Profile Error:", userProfileError);
      return sendApiError(c, "Failed to retrieve user profile", 500);
    }

    const isAdmin = userProfile.role === "admin";
    if (!isAdmin) {
      return sendApiError(
        c,
        "You are not authorized to update this category",
        403
      );
    }

    // ✅ Check if the category exists before updating
    const { data: existingCategory, error: fetchError } = await supabase
      .from("automate_form_temp_categories")
      .select("id")
      .eq("id", categoryId)
      .single();

    if (fetchError || !existingCategory) {
      console.error("Category Not Found:", fetchError);
      return sendApiError(c, "Category not found", 404);
    }

    const updatedCategory = {
      name: body.name || undefined,
      description: body.description || undefined,
      icon: body.icon || undefined,
      updated_at: new Date().toISOString(),
    };

    // Remove undefined fields
    Object.keys(updatedCategory).forEach(
      (key) =>
        updatedCategory[key as keyof typeof updatedCategory] === undefined &&
        delete updatedCategory[key as keyof typeof updatedCategory]
    );

    const { data, error } = await supabase
      .from("temp_categories")
      .update(updatedCategory)
      .eq("id", categoryId)
      .select("id, name, description, icon, created_at, updated_at");

    if (error) {
      console.error("Update Category Error:", error);
      return sendApiError(c, "Failed to update category", 500);
    }

    return sendApiResponse(c, { category: data[0] }, 200);
  } catch (err) {
    console.error("Update Category Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};


const deleteCategory = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const userId = user.user.id;
    const categoryId = c.req.param("id");

    if (!categoryId) {
      return sendApiError(c, "Category ID is required", 400);
    }

    // Fetch user's role
    const { data: userProfile, error: userProfileError } = await supabase
      .from("user_profile")
      .select("role")
      .eq("id", userId)
      .single();

    if (userProfileError || !userProfile) {
      console.error("Get User Profile Error:", userProfileError);
      return sendApiError(c, "Failed to retrieve user profile", 500);
    }

    const isAdmin = userProfile.role === "admin";
    if (!isAdmin) {
      return sendApiError(c, "You are not authorized to delete categories", 403);
    }

    // Check if the category exists
    const { data: category, error: categoryError } = await supabase
      .from("automate_form_temp_categories")
      .select("id")
      .eq("id", categoryId)
      .single();

    if (categoryError || !category) {
      return sendApiError(c, "Category not found", 404);
    }

    // Delete the category
    const { error: deleteError } = await supabase
      .from("automate_form_temp_categories")
      .delete()
      .eq("id", categoryId);

    if (deleteError) {
      console.error("Delete Category Error:", deleteError);
      return sendApiError(c, "Failed to delete category", 500);
    }

    return sendApiResponse(
      c,
      { message: "Category and associated templates deleted successfully" },
      200
    );
  } catch (err) {
    console.error("Delete Category Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

const getTemplatesByCategory = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const userId = user.user.id;
    const categoryId = c.req.param("categoryId");

    if (!categoryId) {
      return sendApiError(c, "Category ID is required", 400);
    }

    // Get user role
    const { data: userProfile, error: userProfileError } = await supabase
      .from("user_profile")
      .select("role")
      .eq("id", userId)
      .single();

    if (userProfileError || !userProfile) {
      return sendApiError(c, "Failed to retrieve user profile", 500);
    }

    const isAdmin = userProfile.role === "admin";

    // Check if category exists
    const { data: category, error: categoryError } = await supabase
      .from("automate_form_temp_categories")
      .select("id")
      .eq("id", categoryId)
      .single();

    if (categoryError || !category) {
      return sendApiError(c, "Category not found", 404);
    }

    // Fetch templates, filter by status if not admin
    let query = supabase
      .from("automate_form_templates")
      .select("id, name, description, template_data, created_at, image_url, status")
      .eq("category_id", categoryId);

    if (!isAdmin) {
      query = query.eq("status", "published"); // Filter for published status
    }

    const { data: templates, error } = await query;

    if (error) {
      console.error("Get Templates Error:", error);
      return sendApiError(c, "Failed to retrieve templates", 500);
    }

    return sendApiResponse(c, { templates }, 200);
  } catch (err) {
    console.error("Get Templates Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};


const createFormTemplate = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const userId = user.user.id;
    const body = await c.req.json();

    // Validate required fields
    if (!body.name || !body.categoryId || !body.template_data) {
      return sendApiError(c, "Name, category, and template data are required", 400);
    }

    // Check if the user is an admin
    const { data: userProfile, error: userProfileError } = await supabase
      .from("user_profile")
      .select("role")
      .eq("id", userId)
      .single();

    if (userProfileError || !userProfile) {
      return sendApiError(c, "Failed to retrieve user profile", 500);
    }

    const isAdmin = userProfile.role === "admin";
    if (!isAdmin) {
      return sendApiError(c, "Only admins can create templates", 403);
    }

    // Ensure the category exists
    const { data: category, error: categoryError } = await supabase
      .from("automate_form_temp_categories")
      .select("id")
      .eq("id", body.categoryId)
      .single();

    if (categoryError || !category) {
      return sendApiError(c, "Category not found", 404);
    }

    const newTemplate = {
      name: body.name,
      description: body.description || null,
      template_data: body.template_data,
      image_url: body.image_url || null,
      category_id: body.categoryId,
    };

    const { data, error } = await supabase
      .from("automate_form_templates")
      .insert([newTemplate])
      .select("id, name, description, template_data, image_url, category_id, created_at");

    if (error) {
      console.error("Create Template Error:", error);
      return sendApiError(c, "Failed to create template", 500);
    }

    return sendApiResponse(c, { template: data[0] }, 201);
  } catch (err) {
    console.error("Create Template Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

const updateFormTemplate = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const userId = user.user.id;
    const templateId = c.req.param("templateId");
console.log("templateId",templateId)
    if (!templateId) {
      return sendApiError(c, "Template ID is required", 400);
    }

    const body = await c.req.json();

    // Ensure at least one field to update is provided
    if (!body.name && !body.description && !body.template_data && !body.image_url && !body.categoryId) {
      return sendApiError(
        c,
        "At least one field (name, description, template_data, image_url) is required to update",
        400
      );
    }

    // Fetch user role to check if the user is admin
    const { data: userProfile, error: userProfileError } = await supabase
      .from("user_profile")
      .select("role")
      .eq("id", userId)
      .single();

    if (userProfileError || !userProfile) {
      console.error("User Profile Fetch Error:", userProfileError);
      return sendApiError(c, "Failed to retrieve user profile", 500);
    }

    if (userProfile.role !== "admin") {
      return sendApiError(c, "Only admins can update templates", 403);
    }

    // Ensure template exists
    const { data: existingTemplate, error: templateError } = await supabase
      .from("automate_form_templates")
      .select("id")
      .eq("id", templateId)
      .single();
console.log("existingTemplate",existingTemplate)
    if (templateError || !existingTemplate) {
      console.log("templateError",templateError)
      return sendApiError(c, "Template not found", 404);
    }

    // Prepare the update data
    const updatedTemplate: Record<string, any> = {
      ...(body.name && { name: body.name }),
      ...(body.description && { description: body.description }),
      ...(body.template_data && { template_data: body.template_data }),
      ...(body.image_url && { image_url: body.image_url }),
      ...(body.categoryId && {category_id:body.categoryId}),
      updated_at: new Date().toISOString(),
    };

    const { data, error } = await supabase
      .from("automate_form_templates")
      .update(updatedTemplate)
      .eq("id", templateId)
      .select("id, name, description, template_data, image_url, created_at");

    if (error) {
      console.error("Update Template Error:", error);
      return sendApiError(c, "Failed to update template", 500);
    }

    return sendApiResponse(c, { template: data[0] }, 200);
  } catch (err) {
    console.error("Update Template Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

const deleteFormTemplate = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const userId = user.user.id;
    const templateId = c.req.param("templateId");

    if (!templateId) {
      return sendApiError(c, "Template ID is required", 400);
    }

    // Check if user is an admin
    const { data: userProfile, error: userProfileError } = await supabase
      .from("user_profile")
      .select("role")
      .eq("id", userId)
      .single();

    if (userProfileError || !userProfile) {
      console.error("User Profile Fetch Error:", userProfileError);
      return sendApiError(c, "Failed to retrieve user profile", 500);
    }

    if (userProfile.role !== "admin") {
      return sendApiError(c, "Only admins can delete templates", 403);
    }

    // Check if template exists
    const { data: template, error: templateError } = await supabase
      .from("automate_form_templates")
      .select("id")
      .eq("id", templateId)
      .single();

    if (templateError || !template) {
      return sendApiError(c, "Template not found", 404);
    }

    // Delete the template
    const { error: deleteError } = await supabase
      .from("automate_form_templates")
      .delete()
      .eq("id", templateId);

    if (deleteError) {
      console.error("Delete Template Error:", deleteError);
      return sendApiError(c, "Failed to delete template", 500);
    }

    return sendApiResponse(c, { message: "Template deleted successfully" }, 200);
  } catch (err) {
    console.error("Delete Template Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

const getTemplateById = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }
    const templateId = c.req.param("templateId");

    if (!templateId) {
      return sendApiError(c, "Template ID is required", 400);
    }

    const { data: template, error: templateError } = await supabase
      .from("automate_form_templates")
      .select("id, name, description, template_data, image_url, category_id")
      .eq("id", templateId)
      .single();

    if (templateError || !template) {
      return sendApiError(c, "Template not found", 404);
    }
    return sendApiResponse(c, { template }, 200);
  } catch (err) {
    console.error("Get Template by ID Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
const toggleTemplateStatus = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const userId = user.user.id;
    const templateId = c.req.param("templateId");

    if (!templateId) {
      return sendApiError(c, "Template ID is required", 400);
    }

    // Fetch user role
    const { data: userProfile, error: userProfileError } = await supabase
      .from("user_profile")
      .select("role")
      .eq("id", userId)
      .single();

    if (userProfileError || !userProfile) {
      console.error("User Profile Fetch Error:", userProfileError);
      return sendApiError(c, "Failed to retrieve user profile", 500);
    }

    if (userProfile.role !== "admin") {
      return sendApiError(c, "Only admins can toggle template status", 403);
    }

    // Get current template status
    const { data: template, error: templateError } = await supabase
      .from("automate_form_templates")
      .select("id, status")
      .eq("id", templateId)
      .single();

    if (templateError || !template) {
      return sendApiError(c, "Template not found", 404);
    }

    // Toggle the status
    const newStatus = template.status === "published" ? "draft" : "published";

    const { data: updatedTemplate, error: updateError } = await supabase
      .from("automate_form_templates")
      .update({ status: newStatus, updated_at: new Date().toISOString() })
      .eq("id", templateId)
      .select("id, name, status");

    if (updateError) {
      console.error("Toggle Status Error:", updateError);
      return sendApiError(c, "Failed to toggle template status", 500);
    }

    return sendApiResponse(
      c,
      {
        message: `Template status changed to "${newStatus}"`,
        template: updatedTemplate[0],
      },
      200
    );
  } catch (err) {
    console.error("Toggle Template Status Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

const cloneTemplate = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const templateId = c.req.param("templateId");

    if (!templateId) {
      return sendApiError(c, "Template ID is required", 400);
    }

    // Fetch the original template
    const { data: template, error: templateError } = await supabase
      .from("automate_form_templates")
      .select("name, description, template_data, image_url, category_id")
      .eq("id", templateId)
      .single();

    if (templateError || !template) {
      return sendApiError(c, "Template not found", 404);
    }
   
    const newTemplate = {
      name: `Copy of ${template.name}`,
      description: template.description,
      template_data: template.template_data,
      image_url: template.image_url,
      category_id: template.category_id,
    };

    const { data, error } = await supabase
      .from("automate_form_templates")
      .insert([newTemplate])
      .select("id, name, description, template_data, image_url, category_id, created_at");

    if (error) {
      console.error("Clone Template Error:", error);
      return sendApiError(c, "Failed to clone template", 500);
    }

    return sendApiResponse(c, { template: data[0] }, 201);
  } catch (err) {
    console.error("Clone Template Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

const getPublishedTemplatesByCategoryPaginated = async (c: Context) => {
  try {
    const categoryId = c.req.param("categoryId");
    if (!categoryId) {
      return sendApiError(c, "Category ID is required", 400);
    }

    // Pagination params
    const page = parseInt(c.req.query("page") || "1", 10);
    const limit = parseInt(c.req.query("limit") || "10", 10);
    const offset = (page - 1) * limit;

    // Check if category exists
    const { data: category, error: categoryError } = await supabase
      .from("automate_form_temp_categories")
      .select("id")
      .eq("id", categoryId)
      .single();
    if (categoryError || !category) {
      return sendApiError(c, "Category not found", 404);
    }

    // Get total count for pagination
    const { count: total, error: countError } = await supabase
      .from("automate_form_templates")
      .select("id", { count: "exact", head: true })
      .eq("category_id", categoryId)
      .eq("status", "published");
    if (countError) {
      return sendApiError(c, "Failed to count templates", 500);
    }

    // Fetch paginated templates
    const { data: templates, error } = await supabase
      .from("automate_form_templates")
      .select("id, name, description, template_data, created_at, image_url, status")
      .eq("category_id", categoryId)
      .eq("status", "published")
      .range(offset, offset + limit - 1);

    if (error) {
      return sendApiError(c, "Failed to retrieve templates", 500);
    }

    return sendApiResponse(
      c,
      {
        templates,
        pagination: {
          page,
          limit,
          total: total || 0,
          totalPages: total ? Math.ceil(total / limit) : 0,
        },
      },
      200
    );
  } catch (err) {
    console.error("Get Published Templates By Category Paginated Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};


export {
  createFormTemplate,
  getCategories,
  getTemplatesByCategory,
  createCategory,
  updateCategory,
  updateFormTemplate,
  deleteFormTemplate,
  deleteCategory,
  getTemplateById,
  toggleTemplateStatus,
  cloneTemplate,
  getPublishedTemplatesByCategoryPaginated
};
