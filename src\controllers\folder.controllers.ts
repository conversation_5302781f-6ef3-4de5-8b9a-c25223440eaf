import type { Context } from "hono";
import { supabase } from "../db";
import { sendApiError, sendApiResponse } from "../utils/Response";

const createFolder = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const body = await c.req.json();
    if (!body.name) {
      return sendApiError(c, "Folder name is required", 400);
    }

    const newFolder = {
      name: body.name,
      created_by: user.user.id,
      parent_id: body.parent_id || null,
    };

    const { data, error } = await supabase
      .from("automate_form_folders")
      .insert([newFolder])
      .select("id, name, created_at, parent_id");

    if (error) {
      console.error("Create Folder Error:", error);
      return sendApiError(c, "Failed to create folder", 500);
    }

    return sendApiResponse(c, { folder: data[0] }, 201);
  } catch (err) {
    console.error("Create Folder Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

const addFormToFolder = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const body = await c.req.json();
    if (!Array.isArray(body.form_ids) || !body.folder_id) {
      return sendApiError(c, "Form IDs (array) and Folder ID are required", 400);
    }

    // Update multiple forms using Supabase
    const { data: updatedForms, error: formError } = await supabase
      .from("automate_forms")
      .update({ folder_id: body.folder_id })
      .in("id", body.form_ids)
      .eq("created_by", user.user.id)
      .select("id, folder_id");

    if (formError) {
      console.error("Form Update Error:", formError);
      return sendApiError(c, "Failed to add forms to folder", 500);
    }

    if (!updatedForms?.length) {
      return sendApiError(c, "No forms updated. Ensure you have access.", 404);
    }

    return sendApiResponse(c, { forms: updatedForms }, 200);
  } catch (err) {
    console.error("Add Forms to Folder Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

  const getFormsInFolder = async (c: Context) => {
    try {
      const user = c.get("user");
      if (!user) {
        return sendApiError(c, "Unauthorized", 401);
      }
  
      const folderId = c.req.param("folder_id");
      if (!folderId) {
        return sendApiError(c, "Folder ID is required", 400);
      }
  
      const { data: forms, error } = await supabase
        .from("automate_forms")
        .select("id, title, description, type, created_at")
        .eq("folder_id", folderId)
        .eq("created_by", user.user.id)
        .eq("is_deleted",false);
  
      if (error) {
        console.error("Get Forms in Folder Error:", error);
        return sendApiError(c, "Failed to retrieve forms", 500);
      }
  
      return sendApiResponse(c, { forms }, 200);
    } catch (err) {
      console.error("Get Forms in Folder Unexpected Error:", err);
      return sendApiError(c, "Internal server error", 500);
    }
  };
 
const getUserFoldersWithForms = async (c: Context) => {
    try {
      const user = c.get("user");
      if (!user) {
        return sendApiError(c, "Unauthorized", 401);
      }
  
      const { data: folders, error } = await supabase
        .from("automate_form_folders")
        .select("id, name, created_at, parent_id")
        .eq("created_by", user.user.id)
        .eq("is_deleted",false);
  
      if (error) {
        console.error("Get User Folders Error:", error);
        return sendApiError(c, "Failed to retrieve folders", 500);
      }
  
      for (let folder of folders) {
        const { data: forms, error: formError } = await supabase
          .from("automate_forms")
          .select("id, title, description, type, created_at,published")
          .eq("folder_id", folder.id)
          .eq("created_by", user.user.id)
          .eq("is_deleted",false);
        
        folder.forms = formError ? [] : forms;
      }
  
      return sendApiResponse(c, { folders }, 200);
    } catch (err) {
      console.error("Get User Folders with Forms Unexpected Error:", err);
      return sendApiError(c, "Internal server error", 500);
    }
  };
  
// const deleteFolder = async (c: Context) => {
//   try {
//     const user = c.get("user");
//     if (!user) {
//       return sendApiError(c, "Unauthorized", 401);
//     }

//     const folderId = c.req.param("folder_id");
//     if (!folderId) {
//       return sendApiError(c, "Folder ID is required", 400);
//     }

//     // First, set folder_id to NULL for all forms inside the folder
//     const { error: updateError } = await supabase
//       .from("forms")
//       .update({ folder_id: null })
//       .eq("folder_id", folderId)
//       .eq("created_by", user.user.id);

//     if (updateError) {
//       console.error("Update Form Error:", updateError);
//       return sendApiError(c, "Failed to remove forms from folder", 500);
//     }

//     // Then, delete the folder
//     const { error: deleteError, data: deletedFolder } = await supabase
//       .from("folders")
//       .delete()
//       .eq("id", folderId)
//       .eq("created_by", user.user.id)

//     if (deleteError) {
//       console.error("Delete Folder Error:", deleteError);
//       return sendApiError(c, "Failed to delete folder", 500);
//     }

//     return sendApiResponse(c, { message: "Folder deleted successfully" }, 200);
//   } catch (err) {
//     console.error("Delete Folder Unexpected Error:", err);
//     return sendApiError(c, "Internal server error", 500);
//   }
// };


const deleteFolders = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const body = await c.req.json();
    const folderIds = body.folder_ids;

    if (!Array.isArray(folderIds) || folderIds.length === 0) {
      return sendApiError(c, "Folder IDs (array) are required", 400);
    }

    // Step 0: Trash all forms in these folders
    const { error: trashFormsError } = await supabase
      .from("automate_forms")
      .update({ is_deleted: true })
      .in("folder_id", folderIds)
      .eq("created_by", user.user.id);

    if (trashFormsError) {
      console.error("Trash Forms Error:", trashFormsError);
      return sendApiError(c, "Failed to trash forms in folders", 500);
    }

    // Step 1: Set folder_id to NULL for all forms inside these folders
    // const { error: updateError } = await supabase
    //   .from("automate_forms")
    //   .update({ folder_id: null })
    //   .in("folder_id", folderIds)
    //   .eq("created_by", user.user.id);

    // if (updateError) {
    //   console.error("Update Form Error:", updateError);
    //   return sendApiError(c, "Failed to remove forms from folders", 500);
    // }

    // Step 2: Delete folders
    const { data: deletedFolders, error: deleteError } = await supabase
      .from("automate_form_folders")
      .update({ is_deleted: true })
      .in("id", folderIds)
      .eq("created_by", user.user.id)
      .select("id, name");

    if (deleteError) {
      console.error("Delete Folder Error:", deleteError);
      return sendApiError(c, "Failed to delete folders", 500);
    }

    if (!deletedFolders?.length) {
      return sendApiError(c, "Folders not found or unauthorized", 404);
    }

    return sendApiResponse(c, {
      message: "Folders deleted successfully",
      folders: deletedFolders,
    }, 200);
  } catch (err) {
    console.error("Delete Folders Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
const deleteFoldersfromTrash = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const body = await c.req.json();
    const folderIds = body.folder_ids;

    if (!Array.isArray(folderIds) || folderIds.length === 0) {
      return sendApiError(c, "Folder IDs (array) are required", 400);
    }

    // Step 0: Trash all forms in these folders
    const { error: trashFormsError } = await supabase
      .from("automate_forms")
      .delete()
      .in("folder_id", folderIds)
      .eq("created_by", user.user.id);

    if (trashFormsError) {
      console.error("delte Forms Error:", trashFormsError);
      return sendApiError(c, "Failed to trash forms in folders", 500);
    }


    // Step 2: Delete folders
    const { data: deletedFolders, error: deleteError } = await supabase
      .from("automate_form_folders")
      .delete()
      .in("id", folderIds)
      .eq("created_by", user.user.id)
      .select("id, name");

    if (deleteError) {
      console.error("Delete Folder Error:", deleteError);
      return sendApiError(c, "Failed to delete folders", 500);
    }

    if (!deletedFolders?.length) {
      return sendApiError(c, "Folders not found or unauthorized", 404);
    }

    return sendApiResponse(c, {
      message: "Folders deleted successfully",
      folders: deletedFolders,
    }, 200);
  } catch (err) {
    console.error("Delete Folders Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
const removeFormsFromFolder = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const body = await c.req.json();
    const { folder_id, form_ids } = body;

    if (!folder_id || !Array.isArray(form_ids) || form_ids.length === 0) {
      return sendApiError(c, "Folder ID and Form IDs (array) are required", 400);
    }

    // Step 1: Validate if forms exist and belong to the folder and user
    const { data: validForms, error: fetchError } = await supabase
      .from("automate_forms")
      .select("id")
      .in("id", form_ids)
      .eq("folder_id", folder_id)
      .eq("created_by", user.user.id);

    if (fetchError) {
      console.error("Fetch Forms Error:", fetchError);
      return sendApiError(c, "Failed to validate forms", 500);
    }

    if (!validForms || validForms.length !== form_ids.length) {
      return sendApiError(c, "Some forms are invalid, not in the folder, or not owned by you", 400);
    }

    // Step 2: Update forms by setting folder_id to NULL
    const { error: updateError } = await supabase
      .from("automate_forms")
      .update({ folder_id: null })
      .in("id", form_ids);

    if (updateError) {
      console.error("Update Forms Error:", updateError);
      return sendApiError(c, "Failed to remove forms from folder", 500);
    }

    return sendApiResponse(c, {
      message: "Forms successfully removed from folder",
      removedFormIds: form_ids,
    }, 200);
  } catch (err) {
    console.error("Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

const getTrashedFolders = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const { data: folders, error } = await supabase
      .from("automate_form_folders")
      .select("id, name, created_at, parent_id")
      .eq("created_by", user.user.id)
      .eq("is_deleted", true);

    if (error) {
      console.error("Get Trashed Folders Error:", error);
      return sendApiError(c, "Failed to retrieve trashed folders", 500);
    }

    return sendApiResponse(c, { folders }, 200);
  } catch (err) {
    console.error("Get Trashed Folders Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

const restoreFolder = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }
    const folderId = c.req.param("id");
    if (!folderId) {
      return sendApiError(c, "Folder ID is required", 400);
    }
    // Check if the folder exists and belongs to the user
    const { data: folder, error: fetchError } = await supabase
      .from("automate_form_folders")
      .select("id, created_by")
      .eq("id", folderId)
      .eq("is_deleted", true)
      .single();
    if (fetchError || !folder) {
      return sendApiError(c, "Trashed folder not found", 404);
    }
    if (folder.created_by !== user.user.id) {
      return sendApiError(c, "Forbidden: You don't have permission to restore this folder", 403);
    }
    // Restore the folder
    const { error: restoreError } = await supabase
      .from("automate_form_folders")
      .update({ is_deleted: false })
      .eq("id", folderId);
    if (restoreError) {
      return sendApiError(c, "Failed to restore folder", 500);
    }
    return sendApiResponse(c, { message: "Folder restored successfully" }, 200);
  } catch (err) {
    return sendApiError(c, "Internal server error", 500);
  }
};

// Exporting all the functions for use in routes
export { createFolder,addFormToFolder,getFormsInFolder,getUserFoldersWithForms,deleteFolders ,removeFormsFromFolder, getTrashedFolders, restoreFolder,deleteFoldersfromTrash };
