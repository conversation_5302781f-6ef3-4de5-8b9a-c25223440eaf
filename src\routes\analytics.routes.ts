import { Hono } from "hono";
import { getFormAnalytics, getWorkspaceAnalytics,getDeviceWiseResponseCount,getResponseTrend, getViewTrend } from "../controllers/analytics.controllers";
import { verifySupabaseAuth } from "../middleware/auth.middleware";

const analyticsRouter = new Hono();

// Apply auth middleware to all routes
analyticsRouter.use("/*", verifySupabaseAuth);

// Routes
analyticsRouter.get("/form/:formId", getFormAnalytics);
analyticsRouter.get("/workspace/:workspaceId", getWorkspaceAnalytics);
analyticsRouter.get("/getDeviceWiseResponseCount/:formId",getDeviceWiseResponseCount)
analyticsRouter.get("/responses-trend",getResponseTrend)
analyticsRouter.get("/views-trend",getViewTrend)


export { analyticsRouter };
