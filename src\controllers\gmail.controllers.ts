import dotenv from "dotenv";
dotenv.config({ path: "./.env" });

import type { Context } from "hono";
import { supabase } from "../db";
import { sendApiError, sendApiResponse } from "../utils/Response";
import { google } from "googleapis";
import { 
  getValidGmailToken, 
  getGmailProfile,
  createDefaultEmailTemplate
} from "../utils/gmailHelper";

const CLIENT_ID = process.env.CLIENT_ID;
const CLIENT_SECRET = process.env.CLIENT_SECRET;
const REDIRECT_URI = process.env.REDIRECT_URI;

/**
 * Get available actions for Gmail integration
 */
const getActionforGmail = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const integrationId = c.req.param("id");
    if (!integrationId) {
      return sendApiError(c, "Integration ID is missing", 400);
    }

    // Fetch actions related to the given integration ID
    const { data: actions, error } = await supabase
      .from("automate_form_integration_actions")
      .select("id, name, description, created_at")
      .eq("integration_id", integrationId);

    if (error) {
      console.error("Error fetching integration actions:", error);
      return sendApiError(c, "Failed to fetch actions", 500);
    }

    return sendApiResponse(c, {
      message: "Integration actions retrieved successfully",
      data: actions,
    });
  } catch (err) {
    console.error("Error fetching integration actions:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

/**
 * Get user's Gmail integration credentials
 */
const getUserIntegrationCredentials = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const integrationId = c.req.param("id");
    if (!integrationId) {
      return sendApiError(c, "Integration ID is missing", 400);
    }

    // Fetch user credentials for the given integration
    const { data: credentials, error } = await supabase
      .from("automate_form_integration_credentials")
      .select("id, auth_type, enabled, created_at, name")
      .eq("integration_id", integrationId)
      .eq("created_by", user.user.id);

    if (error || !credentials) {
      return sendApiError(c, "No credentials found for this integration", 404);
    }

    return sendApiResponse(c, {
      message: "Integration credentials retrieved successfully",
      data: credentials || [],
    });
  } catch (err) {
    console.error("Error fetching user integration credentials:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

/**
 * Add Gmail connection (OAuth flow)
 */
const addGmailConnection = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const { integration_id, name, redirect_uri,formId,formType } = c.req.query();
    
    if (!integration_id || !name || !redirect_uri || !formId || !formType) {
      return sendApiError(c, "Missing required parameters", 400);
    }

    // Validate integration
    const { data: integration, error: integrationError } = await supabase
      .from("automate_form_integrations")
      .select("name")
      .eq("id", integration_id)
      .single();

    if (integrationError || !integration) {
      console.error("Integration Error:", integrationError);
      return sendApiError(c, "Failed to retrieve integration", 500);
    }

    // Generate Google OAuth URL for Gmail
    const oauth2Client = new google.auth.OAuth2(
      CLIENT_ID,
      CLIENT_SECRET,
      REDIRECT_URI
    );

    const scopes = [
      'https://www.googleapis.com/auth/gmail.send',
      'https://www.googleapis.com/auth/userinfo.email',
      'https://www.googleapis.com/auth/userinfo.profile'
    ];

    const state = JSON.stringify({
      userId: user.user.id,
      integration_id,
      name,
      redirect_uri,
      integration_type: 'gmail',
      formId,
      formType,
    });

    const authUrl = oauth2Client.generateAuthUrl({
      access_type: 'offline',
      prompt: 'consent',
      scope: scopes,
      state
    });

    return sendApiResponse(c, { authUrl });
  } catch (err) {
    console.error("Gmail Auth Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

/**
 * Get Gmail profile for a credential
 */
const getProfile = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const { credentialId } = await c.req.json();

    if (!credentialId) {
      return sendApiError(c, "Missing credential ID", 400);
    }

    // Get the valid Gmail token
    const gmailToken = await getValidGmailToken(credentialId);

    if (!gmailToken) {
      return sendApiError(c, "User has not authenticated Gmail", 403);
    }

    // Fetch Gmail profile
    const profile = await getGmailProfile(gmailToken);

    return sendApiResponse(c, {
      message: "Gmail profile retrieved successfully",
      data: profile,
    });
  } catch (err) {
    console.error("Error fetching Gmail profile:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

/**
 * Test email sending
 */
const testEmail = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const { credentialId, testEmail, subject, body } = await c.req.json();

    if (!credentialId || !testEmail) {
      return sendApiError(c, "Missing required parameters", 400);
    }

    // Get the valid Gmail token
    const gmailToken = await getValidGmailToken(credentialId);

    if (!gmailToken) {
      return sendApiError(c, "User has not authenticated Gmail", 403);
    }

    // Get sender email from credentials
    const { data: credential } = await supabase
      .from('automate_form_integration_credentials')
      .select('auth_data')
      .eq('id', credentialId)
      .single();

    const senderEmail = credential?.auth_data?.email || '<EMAIL>';

    // Create test email template
    const testSubject = subject || 'Test Email from AutomateForm.ai';
    const testBody = body || `
      <h2>Test Email</h2>
      <p>This is a test email from your Gmail integration.</p>
      <p>If you received this email, your Gmail integration is working correctly!</p>
      <p>Sent at: ${new Date().toLocaleString()}</p>
    `;

    // Send test email
    const { sendGmailMessage } = await import('../utils/gmailHelper');
    const success = await sendGmailMessage(
      gmailToken,
      testEmail,
      senderEmail,
      testSubject,
      testBody,
      true
    );

    if (!success) {
      return sendApiError(c, "Failed to send test email", 500);
    }

    return sendApiResponse(c, {
      message: "Test email sent successfully",
      data: { sent_to: testEmail }
    });
  } catch (err) {
    console.error("Error sending test email:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

/**
 * Link form to Gmail integration
 */
const linkFormToGmail = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) return sendApiError(c, "Unauthorized", 401);

    let body;
    try {
      body = await c.req.json();
    } catch {
      return sendApiError(c, "Invalid JSON body", 400);
    }

    const { 
      form_id, 
      integration_id, 
      credential_id, 
      action_id, 
      column_mapped_data
    } = body;

    if (!form_id || !integration_id || !credential_id || !action_id) {
      return sendApiError(c, "Missing required parameters", 400);
    }

    const userId = user.user.id;

    // Check if integration already exists
    const { data: existingIntegration, error: checkError } = await supabase
      .from("form_integrations")
      .select("id")
      .eq("form_id", form_id)
      .eq("integration_id", integration_id)
      .eq("credential_id", credential_id)
      .single();

    if (existingIntegration) {
      // Update existing integration
      const { error: updateError } = await supabase
        .from("form_integrations")
        .update({
          action_id,
          mapped_data: column_mapped_data || null,
          updated_at: new Date().toISOString()
        })
        .eq("id", existingIntegration.id);

      if (updateError) {
        console.error("DB Update Error:", updateError);
        return sendApiError(c, "Failed to update form integration", 500);
      }

      return sendApiResponse(c, { message: "Form integration updated successfully" }, 200);
    }

    // Insert new integration if it doesn't exist
    const { error } = await supabase
      .from("form_integrations")
      .insert({
        form_id,
        integration_id,
        credential_id,
        action_id,
        created_by: userId,
        metadata: null,
        mapped_data: column_mapped_data || null,
        enabled: true
      });

    if (error) {
      console.error("DB Insert Error:", error);
      return sendApiError(c, "Failed to save form integration", 500);
    }

    return sendApiResponse(c, { message: "Form integration linked successfully" }, 200);
  } catch (err) {
    console.error("LinkFormToGmail Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

export { 
  getActionforGmail,
  getUserIntegrationCredentials,
  addGmailConnection,
  getProfile,
  testEmail,
  linkFormToGmail
};
