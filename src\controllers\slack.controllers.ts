import dotenv from "dotenv";
dotenv.config({ path: "./.env" });

import type { Context } from "hono";
import { supabase } from "../db";
import { sendApiError, sendApiResponse } from "../utils/Response";
import { 
  getValidSlackToken, 
  getSlackChannels, 
  getSlackUsers,
  createSlackClient
} from "../utils/slackHelper";

const SLACK_CLIENT_ID = process.env.SLACK_CLIENT_ID;
const SLACK_CLIENT_SECRET = process.env.SLACK_CLIENT_SECRET;
const SLACK_REDIRECT_URI = process.env.SLACK_REDIRECT_URI;

/**
 * Get available actions for Slack integration
 */
const getActionforSlack = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const integrationId = c.req.param("id");
    if (!integrationId) {
      return sendApiError(c, "Integration ID is missing", 400);
    }

    // Fetch actions related to the given integration ID
    const { data: actions, error } = await supabase
      .from("automate_form_integration_actions")
      .select("id, name, description, created_at")
      .eq("integration_id", integrationId);

    if (error) {
      console.error("Error fetching integration actions:", error);
      return sendApiError(c, "Failed to fetch actions", 500);
    }

    return sendApiResponse(c, {
      message: "Integration actions retrieved successfully",
      data: actions,
    });
  } catch (err) {
    console.error("Error fetching integration actions:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

/**
 * Get user's Slack integration credentials
 */
const getUserIntegrationCredentials = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const integrationId = c.req.param("id");
    if (!integrationId) {
      return sendApiError(c, "Integration ID is missing", 400);
    }

    // Fetch user credentials for the given integration
    const { data: credentials, error } = await supabase
      .from("automate_form_integration_credentials")
      .select("id, auth_type, enabled, created_at, name")
      .eq("integration_id", integrationId)
      .eq("created_by", user.user.id);

    if (error || !credentials) {
      return sendApiError(c, "No credentials found for this integration", 404);
    }

    return sendApiResponse(c, {
      message: "Integration credentials retrieved successfully",
      data: credentials || [],
    });
  } catch (err) {
    console.error("Error fetching user integration credentials:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

/**
 * Add Slack connection (OAuth flow)
 */
const addSlackConnection = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const { integration_id, name, redirect_uri,formId, formType } = c.req.query();
    
    if (!integration_id || !name || !redirect_uri || !formId || !formType ) {
      return sendApiError(c, "Missing required parameters", 400);
    }

    // Validate integration
    const { data: integration, error: integrationError } = await supabase
      .from("automate_form_integrations")
      .select("name")
      .eq("id", integration_id)
      .single();

    if (integrationError || !integration) {
      console.error("Integration Error:", integrationError);
      return sendApiError(c, "Failed to retrieve integration", 500);
    }

    // Generate Slack OAuth URL with comprehensive scopes
    const scopes = [
      'channels:read',
      'channels:join',
      'channels:history',
      'groups:read',
      'groups:write',
      'chat:write',
      'chat:write.public',
      'users:read',
      'team:read',
      'im:history',
      'im:write',
      'mpim:write'
    ].join(',');

    const state = JSON.stringify({
      userId:user.user.id,
      integration_id,
      name,
      redirect_uri,
       formId,
      formType,
      integration_type: 'slack',
    });

    const authUrl = `https://slack.com/oauth/v2/authorize?` +
      `client_id=${SLACK_CLIENT_ID}&` +
      `scope=${encodeURIComponent(scopes)}&` +
      `redirect_uri=${encodeURIComponent(SLACK_REDIRECT_URI!)}&` +
      `state=${encodeURIComponent(state)}`;

    return sendApiResponse(c,{authUrl});
  } catch (err) {
    console.error("Slack Auth Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

/**
 * Handle Slack OAuth callback
 */
const handleSlackCallback = async (c: Context) => {
  try {
    const { code, state, error } = c.req.query();

    if (error) {
      console.error("Slack OAuth Error:", error);
      return sendApiError(c, `Slack authorization failed: ${error}`, 400);
    }

    if (!code || !state) {
      return sendApiError(c, "Missing authorization code or state", 400);
    }

    // Parse state
    let stateData;
    try {
      stateData = JSON.parse(decodeURIComponent(state));
    } catch (err) {
      return sendApiError(c, "Invalid state parameter", 400);
    }

    const { userId, integration_id,formId,formType, name, redirect_uri,integration_type } = stateData;

    // Exchange code for access token
    const tokenResponse = await fetch('https://slack.com/api/oauth.v2.access', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        client_id: SLACK_CLIENT_ID!,
        client_secret: SLACK_CLIENT_SECRET!,
        code,
        redirect_uri: SLACK_REDIRECT_URI!
      })
    });

    const tokenData = await tokenResponse.json();

    if (!tokenData.ok) {
      console.error("Slack token exchange error:", tokenData);
      return sendApiError(c, "Failed to exchange authorization code", 500);
    }

    // Get user's workspace_id
    const { data: userProfile, error: userProfileError } = await supabase
      .from("user_profile")
      .select("id, workspace_id")
      .eq("id", userId)
      .single();

    if (userProfileError || !userProfile) {
      console.error("User fetch error:", userProfileError);
      return sendApiError(c, "User not found", 404);
    }

    // Store credentials
    const { data: inserted, error: insertError } = await supabase
      .from("automate_form_integration_credentials")
      .insert({
        name,
        created_by: userId,
        workspace_id: userProfile.workspace_id || null,
        integration_id,
        auth_type: "oauth2",
        auth_data: {
          access_token: tokenData.access_token,
          team_id: tokenData.team.id,
          team_name: tokenData.team.name,
          bot_user_id: tokenData.bot_user_id,
          app_id: tokenData.app_id,
          scope: tokenData.scope
        },
        created_at: new Date().toISOString(),
      })
      .select("id")
      .single();

    if (insertError || !inserted) {
      console.error("Insert Error:", insertError);
      return sendApiError(c, "Failed to store credentials", 500);
    }

    // Redirect back to the original page
    return c.redirect(`${redirect_uri}?credential_id=${inserted.id}&formId=${encodeURIComponent(formId)}&formType=${encodeURIComponent(formType)}&integration_type=${integration_type}&success=true`);
  } catch (err) {
    console.error("Slack Callback Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

/**
 * Get Slack channels for a credential
 */
const getChannels = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const { credentialId } = await c.req.json();

    if (!credentialId) {
      return sendApiError(c, "Missing credential ID", 400);
    }

    // Get the valid Slack token
    const slackToken = await getValidSlackToken(credentialId);

    if (!slackToken) {
      return sendApiError(c, "User has not authenticated Slack", 403);
    }

    // Fetch Slack channels
    const channels = await getSlackChannels(slackToken);

    return sendApiResponse(c, {
      message: "Slack channels retrieved successfully",
      data: channels,
    });
  } catch (err) {
    console.error("Error fetching Slack channels:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

/**
 * Get Slack users for a credential
 */
const getUsers = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const { credentialId } = await c.req.json();

    if (!credentialId) {
      return sendApiError(c, "Missing credential ID", 400);
    }

    // Get the valid Slack token
    const slackToken = await getValidSlackToken(credentialId);

    if (!slackToken) {
      return sendApiError(c, "User has not authenticated Slack", 403);
    }

    // Fetch Slack users
    const users = await getSlackUsers(slackToken);

    return sendApiResponse(c, {
      message: "Slack users retrieved successfully",
      data: users,
    });
  } catch (err) {
    console.error("Error fetching Slack users:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

/**
 * Link form to Slack integration
 */
const linkFormToSlack = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) return sendApiError(c, "Unauthorized", 401);

    let body;
    try {
      body = await c.req.json();
    } catch {
      return sendApiError(c, "Invalid JSON body", 400);
    }

    const {
      form_id,
      integration_id,
      credential_id,
      action_id,
      column_mapped_data,
      channel_id,
      channel_name,
      message_template,
      target_user_id
    } = body;

    if (!form_id || !integration_id || !credential_id || !action_id) {
      return sendApiError(c, "Missing required parameters", 400);
    }

    const userId = user.user.id;

    // Check if integration already exists
    const { data: existingIntegration, error: checkError } = await supabase
      .from("form_integrations")
      .select("id")
      .eq("form_id", form_id)
      .eq("integration_id", integration_id)
      .eq("credential_id", credential_id)
      .single();

    if (existingIntegration) {
      // Update existing integration
      const { error: updateError } = await supabase
        .from("form_integrations")
        .update({
          action_id,
          mapped_data: column_mapped_data || null,
          metadata: {
            channel_id: channel_id || null,
            channel_nam:channel_name || null,
            target_user_id:target_user_id || null,
            message_template: message_template || null
          },
          updated_at: new Date().toISOString()
        })
        .eq("id", existingIntegration.id);

      if (updateError) {
        console.error("DB Update Error:", updateError);
        return sendApiError(c, "Failed to update form integration", 500);
      }

      return sendApiResponse(c, { message: "Form integration updated successfully" }, 200);
    }

    // Insert new integration if it doesn't exist
    const { error } = await supabase
      .from("form_integrations")
      .insert({
        form_id,
        integration_id,
        credential_id,
        action_id,
        created_by: userId,
        metadata: {
          channel_id,
          channel_name,
          target_user_id:target_user_id || null,
          message_template: message_template || null
        },
        mapped_data: column_mapped_data || null,
        enabled: true
      });

    if (error) {
      console.error("DB Insert Error:", error);
      return sendApiError(c, "Failed to save form integration", 500);
    }

    return sendApiResponse(c, { message: "Form integration linked successfully" }, 200);
  } catch (err) {
    console.error("LinkFormToSlack Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

/**
 * Update Slack connection
 */
const updateSlackConnection = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) return sendApiError(c, "Unauthorized", 401);

    const userId = user.user.id;
    const { credential_id, name } = await c.req.json();

    if (!credential_id || !name) {
      return sendApiError(c, "Missing required parameters", 400);
    }

    // Verify the credential exists and belongs to the user
    const { data: credential, error: credentialError } = await supabase
      .from("automate_form_integration_credentials")
      .select("id, created_by")
      .eq("id", credential_id)
      .single();

    if (credentialError || !credential) {
      console.error("Credential fetch error:", credentialError);
      return sendApiError(c, "Credential not found", 404);
    }

    // Check if the user has permission to update this credential
    if (credential.created_by !== userId) {
      return sendApiError(c, "You don't have permission to update this connection", 403);
    }

    // Update the credential
    const { data: updated, error: updateError } = await supabase
      .from("automate_form_integration_credentials")
      .update({
        name,
        updated_at: new Date().toISOString(),
      })
      .eq("id", credential_id)
      .select("id")
      .single();

    if (updateError || !updated) {
      console.error("Update Error:", updateError);
      return sendApiError(c, "Failed to update credentials", 500);
    }

    return sendApiResponse(c, updated, 200);

  } catch (err) {
    console.error("UpdateSlackConnection Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};


export {
  getActionforSlack,
  getUserIntegrationCredentials,
  addSlackConnection,
  handleSlackCallback,
  getChannels,
  getUsers,
  linkFormToSlack,
  updateSlackConnection
};
