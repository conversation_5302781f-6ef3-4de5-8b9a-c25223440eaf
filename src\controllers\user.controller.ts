import type { Context } from "hono";
import { supabase } from "../db";
import { sendApiError, sendApiResponse } from "../utils/Response";
import type { LoginRequest, RegisterRequest, ChangePasswordRequest } from "../Interface/user.interface";
import { upload } from "../utils/upload";
import { BUCKET_USER_IMG } from "../constant";
import { getCookie, setCookie, deleteCookie } from "hono/cookie";


const registerUser = async (c: Context) => {
  try {
    const body: RegisterRequest = await c.req.json();
    const { firstName, lastName, email, password, confirmPassword, phone, country, countryCode, terms_conditions,refferal } = body;

    // Validate required fields
    if (!firstName || !lastName || !email || !password || !confirmPassword || !phone || !country || !countryCode || !terms_conditions) {
      return sendApiError(c, "All fields are required!");
    }
    if (!terms_conditions) {
      return sendApiError(c, "You must accept the terms and conditions!");
    }

    // Validate password confirmation
    if (password !== confirmPassword) {
      return sendApiError(c, "Passwords do not match!");
    }

    // Check if the user already exists
    const { data: existingUser, error: existingUserError } = await supabase
      .from("user_profile")
      .select("id")
      .eq("email", email)
      .single();

    if (existingUser) {
      return sendApiError(c, "User already registered!");
    }

    // Register user using Supabase auth
    const { data, error } = await supabase.auth.signUp({ email, password, options: {
        data: {
          referral: refferal
        }
      } });
    if (error) {
      return sendApiError(c, error.message);
    }

    // Clean and validate phone number
    const cleanPhone = phone.replace(/\D/g, '');
    if (cleanPhone.length < 10 || cleanPhone.length > 15) {
      return sendApiError(c, "Invalid phone number.");
    }

    // const numericPhone = BigInt(cleanPhone);

    // Insert user profile
    const { data: userData, error: profileError } = await supabase
      .from("user_profile")
      .upsert([
        {
          id: data?.user?.id,
          first_name: firstName,
          last_name: lastName,
          phone: cleanPhone,
          email,
          country,
          country_code: countryCode,
          terms_conditions
        },
      ])
      .select()
      .single();

    if (profileError) {
      return sendApiError(c, profileError.message);
    }

    // Return success response
    return sendApiResponse(c, {
      message: "Registration successful! Please check your email to verify your account.",
      user: {
        id: userData?.id,
        email: userData?.email,
        firstName: userData?.first_name,
        lastName: userData?.last_name,
      },
    });
  } catch (err) {
    console.error("Registration Error:", err);
    return sendApiError(c, "Internal server error");
  }
};
const loginUser = async (c: Context) => {
  try {
    const body: LoginRequest = await c.req.json();
    const { email, password } = body;

    // Check if email and password are provided
    if (!email || !password) {
      return sendApiError(c, "Email and password are required!");
    }

    // Sign in with Supabase credentials
    const { data, error } = await supabase.auth.signInWithPassword({ email, password });
    
    if (error || !data?.user) {
      return sendApiError(c, error?.message || "Invalid login credentials");
    }
    
    const userId = data.user.id;
    const accessToken = data.session?.access_token;
    const refreshToken = data.session?.refresh_token;

    if (!accessToken || !refreshToken) {
      return sendApiError(c, "Failed to retrieve session tokens", 500);
    }

    // Get user profile from the database with automateform role information
    const { data: user, error: dbError } = await supabase
      .from("user_profile")
      .select(`
        id, 
        first_name, 
        last_name, 
        workspace_id, 
        role, 
        automateform_members (
          id,
          workspace_id,
          role_id,
          automateform_role (
            id,
            name,
            description
          )
        )
      `)
      .eq("id", userId)
      .single();
    
    if (dbError || !user) {
      console.error("Database Error:", dbError);
      return sendApiError(c, "Failed to retrieve user data", 500);
    }
  
    const fullName = `${user.first_name} ${user.last_name}`.trim();
   
    // Set cookies for access and refresh tokens
    setCookie(c, "access_token", accessToken, {
      httpOnly: true,
      secure: true,
      sameSite: "None",
      maxAge: 60 * 60 * 24, // 1 day
    });

    setCookie(c, "refresh_token", refreshToken, {
      httpOnly: true,
      secure: true,
      sameSite: "None",
      maxAge: 60 * 60 * 24 * 7, // 7 days
    });

  

    // Return the user data along with role
    return sendApiResponse(c, {
      email: data.user.email,
      name: fullName,
      workspace_id: user.workspace_id || null,
      custom_role_id: user.automateform_members?.[0]?.role_id || null,
      role: user.role,
      custom_role: user.automateform_members?.[0]?.automateform_role?.name || null,
      access_token: accessToken,
      isOnboardingComplete: Boolean(user.workspace_id),
    });
    
  } catch (err) {
    console.error("Login Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
// const updateUserRole = async (c: Context) => {
//   const user = c.get("user");
//   console.log("user", user);
//   if (!user) {
//     return sendApiError(c, "Unauthorized", 401);
//   }
//   try {
//     const body = await c.req.json();
//     const { newRole } = body;
//     console.log("newRole", newRole);
//     if (!newRole) {
//       return sendApiError(c, "newRole are required");
//     }

//     // Validate requester
//     const { data: requesterData, error: authError } = await supabase.auth.admin.getUserById(user.user.id);
//     console.log("requesterData", requesterData);
//     console.log("authError", authError);
//     if (authError || !requesterData?.user) {
//       return sendApiError(c, "Invalid token", 401);
//     }
//     console.log("requesterData", requesterData);
//     // Check if requester is admin
//     const requesterRole = requesterData.user.user_metadata?.role;
//     if (requesterRole !== "admin") {
//       return sendApiError(c, "Only admins can update user roles", 403);
//     }

//     // Update target user's role
//     const { data, error } = await supabase.auth.admin.updateUserById(user.user.id, {
//       user_metadata: {
//         role: newRole,
//       },
//     });

//     if (error) {
//       return sendApiError(c, error.message);
//     }

//     return sendApiResponse(c, {
//       message: `Role updated to '${newRole}' for user ${user.user.email}`,
//       user: {
//         id: data.user?.id,
//         email: data.user?.email,
//         newRole: data.user?.user_metadata?.role,
//       },
//     });
//   } catch (err) {
//     console.error("Update Role Error:", err);
//     return sendApiError(c, "Internal server error");
//   }
// };
const logoutUser = async (c: Context) => {
  try {
    const user = c.get("user");

    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }
    const { error: logoutError } = await supabase.auth.signOut();

    if (logoutError) {
      console.error("Supabase Logout Error:", logoutError);
      return sendApiError(c, "Failed to log out", 500);
    }

    const options = { httpOnly: true, secure: true };
    deleteCookie(c, "access_token", options);
    deleteCookie(c, "refresh_token", options);

    return sendApiResponse(c, { message: "Logout successful" });
  } catch (err) {
    console.error("Logout Error:", err);
    return sendApiError(c, "Internal server error");
  }
};
const uploadProfilePic = async (c: Context) => {
  const body = await c.req.parseBody();
  const file = body["profilePic"] as File | undefined;

  if (!file) {
    return sendApiError(c, "Profile picture is required", 400);
  }

  try {
    const user = c.get("user");
    console.log("user", user);
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const imageUrl = await upload(file, BUCKET_USER_IMG);
    console.log("imageUrl", imageUrl);
    const { data, error } = await supabase
      .from("user_profile")
      .update({ profile_image: imageUrl })
      .eq("id", user.user.id)
      .select("profile_image")
      .single();

    console.log("data", data);

    if (error) {
      throw error;
    }

    return sendApiResponse(c, {
      message: "Profile picture uploaded successfully",
      imageUrl: data?.profile_image
    });
  } catch (err) {
    console.error("Upload Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
const refreshSession = async (c: Context) => {
  try {
    const currentRefreshToken =
      getCookie(c, "refresh_token") ||
      c.req.header("Authorization")?.replace("Bearer ", "");

      console.log("currentRefreshToken", currentRefreshToken);
    if (!currentRefreshToken) {
      return sendApiError(c, "No refresh token provided", 401);
    }

    const { data, error } = await supabase.auth.refreshSession({ refresh_token: currentRefreshToken });
   console.log("data", data);
    if (error || !data?.session) {
      console.log("Refresh Error:", error);
      return sendApiError(c, "Session refresh failed", 401);
    }
    const accessToken = data.session.access_token;
    const refreshToken = data.session.refresh_token;


    setCookie(c, "access_token", accessToken, {
      httpOnly: true,
      secure: true,
      sameSite: "None",
      maxAge: 60 * 60 * 24,
    });

    setCookie(c, "refresh_token", refreshToken, {
      httpOnly: true,
      secure: true,
      sameSite: "None",
      maxAge: 60 * 60 * 24 * 7,
    });

    return sendApiResponse(c, {
      message: "Session refreshed",
      accessToken,
    });

  } catch (err) {
    return sendApiError(c, "Internal server error", 500);
  }
};
const changePassword = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }
    const body: ChangePasswordRequest = await c.req.json();
    const { oldPassword, newPassword, confirmNewPassword } = body;

    if (!oldPassword || !newPassword || !confirmNewPassword) {
      return sendApiError(c, "All fields are required!");
    }

    if (newPassword !== confirmNewPassword) {
      return sendApiError(c, "New passwords do not match!");
    }

    if (newPassword.length < 8) {
      return sendApiError(c, "Password must be at least 8 characters long!");
    }

    // Verify the old password
    const { error } = await supabase.auth.signInWithPassword({
      email: user.user.email,
      password: oldPassword,
    });

    if (error) {
      return sendApiError(c, "Old password is incorrect!");
    }

    const { error: updateError } = await supabase.auth.updateUser({
      password: newPassword,
    });

    if (updateError) {
      return sendApiError(c, updateError.message);
    }

    return sendApiResponse(c, { message: "Password changed successfully!" });

  } catch (err) {
    console.error("Change Password Error:", err);
    return sendApiError(c, "Internal server error");
  }
};
const updateUserProfile = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const body: Partial<{
      first_name: string;
      last_name: string;
      phone: string;
      timezone: string;
      language: string;
      
    }> = await c.req.json();

    const { first_name, last_name, phone, timezone, language } = body;

    // Validate at least one field is provided for update
    if (!first_name && !last_name && !phone && !timezone && !language) {
      return sendApiError(c, "No update fields provided", 400);
    }


    const updateData: Record<string, any> = {};
    if (first_name) updateData.first_name = first_name;
    if (last_name) updateData.last_name = last_name;
    if (phone) updateData.phone = phone;
    if (timezone) updateData.time_zone = timezone;
    if (language) updateData.language = language;
    


    // Update user profile in database
    const { error } = await supabase
      .from("user_profile")
      .update(updateData)
      .eq("id", user.user.id)
      .select()
      .single();

    if (error) {
      console.error("Database Error:", error);
      return sendApiError(c, "Failed to update profile");
    }

    return sendApiResponse(c, {
      message: "Profile updated successfully!",
      updatedFields: updateData,
    });

  } catch (err) {
    console.error("Update Profile Error:", err);
    return sendApiError(c, "Internal server error");
  }
};
const loginWithGoogle = async (c: Context) => {
  try {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: "google",
      options: {
        redirectTo: "https://automateforms.ai/",
      },
    });

    if (error) {
      return sendApiError(c, error.message, 400);
    }
    return c.redirect(data.url);
  } catch (err) {
    console.error("Google Login Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
const googleCallback = async (c: Context) => {
  console.log("google callback hit")
  try {
    const { code, redirect_uri } = c.req.query(); // Get redirect URI if passed
    if (!code) return sendApiError(c, "Authorization code is missing", 400);
    console.log("code", code)
    console.log("redirect_uri", redirect_uri)
    // Exchange code for session
    const { data, error } = await supabase.auth.exchangeCodeForSession(code);
    if (error) {
      console.log("error", error);
      return sendApiError(c, error.message, 400);
    }

    const user = data.user;
    console.log("User Data:", user);

    if (!user) {
      return sendApiError(c, "Failed to retrieve user data", 400);
    }

    // Check if user exists in the database
    const { data: existingUser, error: userError } = await supabase
      .from("users")
      .select("id, email")
      .eq("id", user.id)
      .single();

    if (userError || !existingUser) {
      console.log("Creating new user...");
      // Insert new user into database
      const { error: insertError } = await supabase.from("users").insert({
        id: user.id,
        email: user.email,
        name: user.user_metadata.full_name,
        avatar_url: user.user_metadata.avatar_url,
      });

      if (insertError) {
        return sendApiError(c, insertError.message, 400);
      }
    }

    // Store session token in a cookie
    setCookie(c, "session_token", data.session.access_token, {
      httpOnly: true,
      secure: true,
      sameSite: "Strict",
      maxAge: 60 * 60 * 24 * 7, // 7 days
    });

    // Redirect the user to the desired page
    const redirectUrl = redirect_uri || "https://your-frontend-url/dashboard";
    return c.redirect(redirectUrl);
  } catch (err) {
    console.error("Google Callback Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
const forgotPassword = async (c: Context) => {
  try {
    const { email, redirect_url } = await c.req.json();

    if (!email) {
      return sendApiError(c, "Email is required!", 400);
    }
    if (!redirect_url) {
      return sendApiError(c, "redirect_url is required!", 400);
    }
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: redirect_url,
    });

    if (error) {
      console.error("Forgot Password Error:", error);
      return sendApiError(c, error.message || "Failed to send reset password email");
    }

    return sendApiResponse(c, {
      message: "Password reset email sent successfully!",
    });

  } catch (err) {
    console.error("Forgot Password Error:", err);
    return sendApiError(c, "Internal server error");
  }
};
const resetPassword = async (c: Context) => {
  try {
    const { token, newPassword } = await c.req.json();

    if (!token || !newPassword) {
      return sendApiError(c, "Token and new password are required!", 400);
    }

    const { error } = await supabase.auth.updateUser({
      password: newPassword,
    });

    if (error) {
      console.error("Reset Password Error:", error);
      return sendApiError(c, error.message || "Failed to reset password");
    }

    return sendApiResponse(c, {
      message: "Password updated successfully!",
    });

  } catch (err) {
    console.error("Reset Password Error:", err);
    return sendApiError(c, "Internal server error");
  }
};
const getUser = async (c: Context) => {
  try {
    const user = c.get("user");

    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    console.log("Authenticated User:", user.user.id);

    const { data: userdata, error: dbError } = await supabase
      .from("user_profile")
      .select(`
        id, 
        first_name, 
        last_name, 
        workspace_id, 
        role, 
        email,
        phone,
        country,
        country_code,
        time_zone,
        profile_image,
        automateform_members (
          id,
          workspace_id,
          role_id,
          automateform_role (
            id,
            name,
            description
          )
        )
      `)
      .eq("id", user.user.id)
      .single();
    
    if (dbError || !userdata) {
      console.error("Database Error:", dbError);
      return sendApiError(c, "Failed to retrieve user data", 500);
    }

    const userData={
      user_id:userdata.id,
      email: userdata.email,
      first_name:userdata.first_name,
      last_name:userdata.last_name,
      country:userdata.country,
      country_code:userdata.country_code,
      timezone:userdata.time_zone,
      profile_image:userdata.profile_image,
      phone:userdata.phone,
      role: userdata.role,
      workspace_id: userdata.workspace_id || null,
      custom_role_id: userdata.automateform_members?.[0]?.role_id || null,
      custom_role: userdata.automateform_members?.[0]?.automateform_role?.name || null,
      isOnboardingComplete: Boolean(userdata.workspace_id),
    }
    return sendApiResponse(c,{
      user:userData,
      message: "User fetched successfully",
    });
  } catch (err) {
    console.error("Unexpected Error in Fetching User:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
const getUserRole = async (c: Context) => {
  try {
    const user = c.get("user");

    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    

    const { data, error } = await supabase
      .from("automateform_members")
      .select(
        'role_id'
      )
      .eq("user_id", user.user.id)
      .single();

    if (error || !data) {
      console.error("members Error:", error);
      return sendApiError(c, "member not found", 404);
    }

    return sendApiResponse(c, {
      message: "role fetched successfully",
      role_id: data.role_id,
    });
  } catch (err) {
    console.error("Unexpected Error in Fetching role:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
const createProfile=async (c: Context) => {
   try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }
    const body: RegisterRequest = await c.req.json();
    const { firstName, lastName, email, phone, country, countryCode, terms_conditions } = body;

    // Validate required fields
    if (!firstName || !lastName || !email || !phone || !country || !countryCode || !terms_conditions) {
      return sendApiError(c, "All fields are required!");
    }
    if (!terms_conditions) {
      return sendApiError(c, "You must accept the terms and conditions!");
    }

  

    // Check if the user already exists
    const { data: existingUser, error: existingUserError } = await supabase
      .from("user_profile")
      .select("id")
      .eq("email", email)
      .single();

    if (existingUser) {
      return sendApiError(c, "User already registered!");
    }

    // Clean and validate phone number
    const cleanPhone = phone.replace(/\D/g, '');
    if (cleanPhone.length < 10 || cleanPhone.length > 15) {
      return sendApiError(c, "Invalid phone number.");
    }

    // Insert user profile
    const { data: userData, error: profileError } = await supabase
      .from("user_profile")
      .upsert([
        {
          id: user?.user?.id,
          first_name: firstName,
          last_name: lastName,
          phone: cleanPhone,
          email,
          country,
          country_code: countryCode,
          terms_conditions
        },
      ])
      .select()
      .single();

    if (profileError) {
      return sendApiError(c, profileError.message);
    }

    // Return success response
    return sendApiResponse(c, {
      message: "Registration successful! Please check your email to verify your account.",
      user: {
        id: userData?.id,
        email: userData?.email,
        firstName: userData?.first_name,
        lastName: userData?.last_name,
      },
    });
  } catch (err) {
    console.error("Registration Error:", err);
    return sendApiError(c, "Internal server error");
  }
};
const checkAuth = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }
    return sendApiResponse(c, {
      authenticated: true,
      user: user.user
    });
  } catch (err) {
    console.error("Error in checkAuth:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
const removeProfileImage = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    // Get current profile image URL
    const { data: userData, error: fetchError } = await supabase
      .from("user_profile")
      .select("profile_image")
      .eq("id", user.user.id)
      .single();
    
    if (fetchError) {
      console.error("Fetch User Error:", fetchError);
      return sendApiError(c, "Failed to retrieve user data", 500);
    }
    
    const currentImageUrl = userData?.profile_image;
    
    // If there's no image to remove, return early
    if (!currentImageUrl) {
      return sendApiResponse(c, { 
        message: "No profile image to remove" 
      });
    }
    
    // Extract file path from URL
    // Example URL: https://xxxx.supabase.co/storage/v1/object/public/profileimage/filename.jpg
    const urlParts = currentImageUrl.split('/');
    const fileName = urlParts[urlParts.length - 1];
    const filePath = fileName;
    
    // Try to remove the file from storage
    try {
      const { error: storageError } = await supabase.storage
        .from(BUCKET_USER_IMG)
        .remove([filePath]);
        
      if (storageError) {
        console.error("Storage Removal Error:", storageError);
        // Continue anyway to update the database
      }
    } catch (storageErr) {
      console.error("Storage Operation Error:", storageErr);
      // Continue anyway to update the database
    }
    
    // Update user profile to remove image reference
    const { error: updateError } = await supabase
      .from("user_profile")
      .update({ profile_image: null })
      .eq("id", user.user.id);
    
    if (updateError) {
      console.error("Update Profile Error:", updateError);
      return sendApiError(c, "Failed to update profile", 500);
    }
    
    return sendApiResponse(c, {
      message: "Profile image removed successfully"
    });
    
  } catch (err) {
    console.error("Remove Profile Image Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
const deleteUser = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    // Start a transaction to ensure data consistency
    const { error: transactionError } = await supabase.rpc('delete_user_data', {
      user_id: user.user.id
    });

    if (transactionError) {
      console.error("Transaction Error:", transactionError);
      return sendApiError(c, "Failed to delete user data", 500);
    }

    // Delete user's profile image if exists
    const { data: userData, error: fetchError } = await supabase
      .from("user_profile")
      .select("profile_image")
      .eq("id", user.user.id)
      .single();
    
    if (userData?.profile_image) {
      const urlParts = userData.profile_image.split('/');
      const fileName = urlParts[urlParts.length - 1];
      
      await supabase.storage
        .from(BUCKET_USER_IMG)
        .remove([fileName]);
    }

    // Delete user from Supabase Auth
    const { error: authError } = await supabase.auth.admin.deleteUser(
      user.user.id
    );

    if (authError) {
      console.error("Auth Deletion Error:", authError);
      return sendApiError(c, "Failed to delete user account", 500);
    }

    // Clear auth cookies
    const options = { httpOnly: true, secure: true };
    deleteCookie(c, "access_token", options);
    deleteCookie(c, "refresh_token", options);

    return sendApiResponse(c, {
      message: "User account and all associated data deleted successfully"
    });

  } catch (err) {
    console.error("Delete User Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
export { loginUser, registerUser, logoutUser, uploadProfilePic, refreshSession, changePassword, updateUserProfile, loginWithGoogle, googleCallback, forgotPassword, resetPassword, getUser, getUserRole, createProfile, checkAuth, removeProfileImage, deleteUser };
