import { describe, it, expect, beforeAll, afterAll } from "bun:test";
import { app } from "../src/app";

// Mock data for testing
const mockBannerData = {
  name: "Test Banner",
  category: "test",
};

const mockFile = new File(["test content"], "test-banner.jpg", {
  type: "image/jpeg",
});

describe("Banner API", () => {
  let bannerId: string;
  let authToken: string;

  beforeAll(async () => {
    // Setup: You would typically authenticate and get a token here
    // authToken = await getAuthToken();
    authToken = "mock-token"; // Replace with actual token in real tests
  });

  afterAll(async () => {
    // Cleanup: Delete test banner if it exists
    if (bannerId) {
      await app.request(`/v1/banners/${bannerId}`, {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${authToken}`,
        },
      });
    }
  });

  it("should create a new banner", async () => {
    const formData = new FormData();
    formData.append("name", mockBannerData.name);
    formData.append("category", mockBannerData.category);
    formData.append("banner", mockFile);

    const response = await app.request("/v1/banners", {
      method: "POST",
      headers: {
        Authorization: `Bearer ${authToken}`,
      },
      body: formData,
    });

    expect(response.status).toBe(201);
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data.banner.name).toBe(mockBannerData.name);
    expect(data.data.banner.category).toBe(mockBannerData.category);
    expect(data.data.banner.banner_url).toBeDefined();
    
    bannerId = data.data.banner.id;
  });

  it("should get all banners", async () => {
    const response = await app.request("/v1/banners", {
      method: "GET",
      headers: {
        Authorization: `Bearer ${authToken}`,
      },
    });

    expect(response.status).toBe(200);
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(Array.isArray(data.data.banners)).toBe(true);
    expect(data.data.pagination).toBeDefined();
  });

  it("should get banners with category filter", async () => {
    const response = await app.request("/v1/banners?category=test", {
      method: "GET",
      headers: {
        Authorization: `Bearer ${authToken}`,
      },
    });

    expect(response.status).toBe(200);
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(Array.isArray(data.data.banners)).toBe(true);
  });

  it("should get banner by ID", async () => {
    if (!bannerId) return;

    const response = await app.request(`/v1/banners/${bannerId}`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${authToken}`,
      },
    });

    expect(response.status).toBe(200);
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data.banner.id).toBe(bannerId);
  });

  it("should update a banner", async () => {
    if (!bannerId) return;

    const formData = new FormData();
    formData.append("name", "Updated Test Banner");

    const response = await app.request(`/v1/banners/${bannerId}`, {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${authToken}`,
      },
      body: formData,
    });

    expect(response.status).toBe(200);
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data.banner.name).toBe("Updated Test Banner");
  });

  it("should get banner categories", async () => {
    const response = await app.request("/v1/banners/categories", {
      method: "GET",
      headers: {
        Authorization: `Bearer ${authToken}`,
      },
    });

    expect(response.status).toBe(200);
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(Array.isArray(data.data.categories)).toBe(true);
  });

  it("should get banners grouped by category", async () => {
    const response = await app.request("/v1/banners/grouped", {
      method: "GET",
      headers: {
        Authorization: `Bearer ${authToken}`,
      },
    });

    expect(response.status).toBe(200);
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(Array.isArray(data.data.banners_by_category)).toBe(true);
  });

  it("should delete a banner", async () => {
    if (!bannerId) return;

    const response = await app.request(`/v1/banners/${bannerId}`, {
      method: "DELETE",
      headers: {
        Authorization: `Bearer ${authToken}`,
      },
    });

    expect(response.status).toBe(200);
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.data.message).toBe("Banner deleted successfully");
    
    // Clear bannerId so cleanup doesn't try to delete again
    bannerId = "";
  });

  it("should return 404 for non-existent banner", async () => {
    const response = await app.request("/v1/banners/non-existent-id", {
      method: "GET",
      headers: {
        Authorization: `Bearer ${authToken}`,
      },
    });

    expect(response.status).toBe(404);
    const data = await response.json();
    expect(data.success).toBe(false);
  });

  it("should return 400 for missing required fields", async () => {
    const formData = new FormData();
    formData.append("name", "Test Banner");
    // Missing category and banner file

    const response = await app.request("/v1/banners", {
      method: "POST",
      headers: {
        Authorization: `Bearer ${authToken}`,
      },
      body: formData,
    });

    expect(response.status).toBe(400);
    const data = await response.json();
    expect(data.success).toBe(false);
  });
});
