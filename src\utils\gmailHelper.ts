import { google } from 'googleapis';
import { supabase } from '../db';

interface GmailCredentials {
  access_token: string;
  refresh_token?: string;
  email: string;
  name?: string;
}

interface EmailTemplate {
  subject: string;
  body: string;
  isHtml: boolean;
}

interface EmailAttachment {
  filename: string;
  content: string;
  contentType: string;
}

/**
 * Get valid Gmail access token from credentials
 */
export const getValidGmailToken = async (credentialId: string): Promise<string | null> => {
  try {
    const { data: credential, error } = await supabase
      .from('automate_form_integration_credentials')
      .select('auth_data')
      .eq('id', credentialId)
      .single();

    if (error || !credential) {
      console.error('Failed to fetch Gmail credentials:', error);
      return null;
    }

    const authData = credential.auth_data as GmailCredentials;
    return authData.access_token || null;
  } catch (error) {
    console.error('Error getting Gmail token:', error);
    return null;
  }
};

/**
 * Create Gmail API client
 */
export const createGmailClient = (token: string) => {
  const oauth2Client = new google.auth.OAuth2();
  oauth2Client.setCredentials({ access_token: token });
  return google.gmail({ version: 'v1', auth: oauth2Client });
};

/**
 * Get Gmail user profile
 */
export const getGmailProfile = async (token: string) => {
  try {
    const gmail = createGmailClient(token);
    const response = await gmail.users.getProfile({ userId: 'me' });
    return response.data;
  } catch (error) {
    console.error('Error fetching Gmail profile:', error);
    throw error;
  }
};

/**
 * Create email message in RFC 2822 format
 */
export const createEmailMessage = (
  to: string,
  from: string,
  subject: string,
  body: string,
  isHtml: boolean = false,
  attachments?: EmailAttachment[],
  cc?: string,
  bcc?: string
): string => {
  const boundary = `boundary_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  
  let message = [
    `To: ${to.split(',').map(e => e.trim()).join(', ')}`,
    `From: ${from}`,
    `Subject: ${subject}`,
    'MIME-Version: 1.0'
  ];
  if (cc) {
    message.push(`Cc: ${cc.split(',').map(e => e.trim()).join(', ')}`);
  }
  if (bcc) {
    message.push(`Bcc: ${bcc.split(',').map(e => e.trim()).join(', ')}`);
  }

  if (attachments && attachments.length > 0) {
    message.push(`Content-Type: multipart/mixed; boundary="${boundary}"`);
    message.push('');
    
    // Email body part
    message.push(`--${boundary}`);
    message.push(`Content-Type: ${isHtml ? 'text/html' : 'text/plain'}; charset=utf-8`);
    message.push('Content-Transfer-Encoding: quoted-printable');
    message.push('');
    message.push(body);
    
    // Attachment parts
    attachments.forEach(attachment => {
      message.push(`--${boundary}`);
      message.push(`Content-Type: ${attachment.contentType}`);
      message.push(`Content-Disposition: attachment; filename="${attachment.filename}"`);
      message.push('Content-Transfer-Encoding: base64');
      message.push('');
      message.push(attachment.content);
    });
    
    message.push(`--${boundary}--`);
  } else {
    message.push(`Content-Type: ${isHtml ? 'text/html' : 'text/plain'}; charset=utf-8`);
    message.push('');
    message.push(body);
  }

  return message.join('\r\n');
};

/**
 * Send email via Gmail API
 */
export const sendGmailMessage = async (
  token: string,
  to: string,
  from: string,
  subject: string,
  body: string,
  isHtml: boolean = false,
  cc?: string,
  bcc?: string,
  attachments?: EmailAttachment[]
): Promise<boolean> => {
  try {
    const gmail = createGmailClient(token);
    
    const emailMessage = createEmailMessage(to, from, subject, body, isHtml, attachments, cc, bcc);
    const encodedMessage = Buffer.from(emailMessage).toString('base64')
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=+$/, '');

    const response = await gmail.users.messages.send({
      userId: 'me',
      requestBody: {
        raw: encodedMessage
      }
    });

    console.log(`✅ Email sent to ${to}, Message ID: ${response.data.id}`);
    return true;
  } catch (error) {
    console.error('Error sending Gmail message:', error);
    return false;
  }
};

/**
 * Process email template with form data
 */
export const processEmailTemplate = (
  template: string,
  formData: any[],
  submittedAt: string,
  formTitle: string
): string => {
  let processedTemplate = template;
  
  // Replace form-specific variables
  processedTemplate = processedTemplate.replace(/\{\{form_title\}\}/g, formTitle);
  processedTemplate = processedTemplate.replace(/\{\{submitted_at\}\}/g, new Date(submittedAt).toLocaleString());
  processedTemplate = processedTemplate.replace(/\{\{submission_date\}\}/g, new Date(submittedAt).toLocaleDateString());
  processedTemplate = processedTemplate.replace(/\{\{submission_time\}\}/g, new Date(submittedAt).toLocaleTimeString());
  
  // Create form data map for easy access
  const formDataMap: any = {};
  formData.forEach((field: any) => {
    formDataMap[field.id] = field;
  });
  
  // Replace field variables like {{field_id.value}}
  const fieldRegex = /\{\{([^}]+)\}\}/g;
  processedTemplate = processedTemplate.replace(fieldRegex, (match: string, content: string) => {
    const parts = content.split('.');
    if (parts.length >= 1) {
      const fieldId = parts[0];
      const field = formDataMap[fieldId];
      
      if (field) {
        if (parts.length > 2) {
          // Nested property like {{field_id.value.firstName}}
          const nestedKey = parts[2];
          if (field.value && typeof field.value === 'object') {
            return field.value[nestedKey] || '';
          }
        } else if (parts.length === 1) {
          // Just field ID {{field_id}}
          return field.value || '';
        } else if (parts.length === 2 && parts[1] === 'fullname') {
          // Full name {{field_id.fullname}}
          if (field.value && typeof field.value === 'object' && 
              field.value.firstName && field.value.lastName) {
            return `${field.value.firstName} ${field.value.lastName}`;
          }
          return field.value || '';
        } else if (parts.length >= 2) {
          // Property access {{field_id.value}}
          const propertyName = parts[parts.length - 1];
          if (field.value && typeof field.value === 'object') {
            return field.value[propertyName] || '';
          }
          return field.value || '';
        }
      }
    }
    return match; // Return original if no match found
  });
  
  return processedTemplate;
};

/**
 * Create default email template
 */
export const createDefaultEmailTemplate = (
  formTitle: string,
  formData: any[],
  submittedAt: string
): { subject: string; body: string } => {
  const subject = `Thank you for your submission - ${formTitle}`;
  
  let body = `
<!DOCTYPE html>
<html>
<head>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .content { background-color: #ffffff; padding: 20px; border: 1px solid #e9ecef; border-radius: 8px; }
        .field { margin-bottom: 15px; }
        .field-label { font-weight: bold; color: #495057; }
        .field-value { margin-top: 5px; padding: 8px; background-color: #f8f9fa; border-radius: 4px; }
        .footer { margin-top: 20px; padding-top: 20px; border-top: 1px solid #e9ecef; font-size: 12px; color: #6c757d; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>Thank you for your submission!</h2>
            <p>We have received your response for <strong>${formTitle}</strong></p>
        </div>
        
        <div class="content">
            <h3>Your Submission Details:</h3>
            <p><strong>Submitted on:</strong> ${new Date(submittedAt).toLocaleString()}</p>
            
            <div class="fields">`;

  // Add form fields
  formData.forEach((field: any) => {
    let value = field.value;
    
    // Handle different field types
    if (typeof value === 'object' && value !== null) {
      if (value.firstName && value.lastName) {
        value = `${value.firstName} ${value.lastName}`;
      } else {
        value = JSON.stringify(value);
      }
    }
    
    body += `
                <div class="field">
                    <div class="field-label">${field.title || field.name}:</div>
                    <div class="field-value">${value || 'N/A'}</div>
                </div>`;
  });

  body += `
            </div>
        </div>
        
        <div class="footer">
            <p>This is an automated email from AutomateForm.ai</p>
            <p>If you have any questions, please contact our support team.</p>
        </div>
    </div>
</body>
</html>`;

  return { subject, body };
};

/**
 * Extract email from form data
 */
export const extractEmailFromFormData = (formData: any[]): string | null => {
  // Look for email field by common patterns
  const emailField = formData.find((field: any) => {
    const fieldName = (field.name || '').toLowerCase();
    const fieldTitle = (field.title || '').toLowerCase();
    const fieldType = (field.type || '').toLowerCase();
    
    return fieldType === 'email' || 
           fieldName.includes('email') || 
           fieldTitle.includes('email') ||
           fieldName.includes('e-mail') ||
           fieldTitle.includes('e-mail');
  });
  
  return emailField?.value || null;
};
