import type { Context } from "hono";
import { supabase } from "../db";
import { sendApiError, sendApiResponse } from "../utils/Response";
import { parse } from "json2csv";

const getResponse = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }
    const formId = c.req.param("form_id");
    if (!formId) {
      return sendApiError(c, "Form ID is required", 400);
    }
    const limit = parseInt(c.req.query("limit") || "10", 10); // Default limit to 10
    const offset = parseInt(c.req.query("offset") || "0", 10); 
    const startDate = c.req.query("start_date");
    const endDate = c.req.query("end_date");
    const { data: form, error: formError } = await supabase
      .from("automate_forms")
      .select("id, created_by")
      .eq("id", formId)
      .single();

    if (formError || !form) {
      return sendApiError(c, "Form not found or unauthorized", 403);
    }

    let query = supabase
    .from("automate_form_responses")
    .select("id, form_id, answers, submitted_at",{ count: "exact" })
    .eq("form_id", formId)
    .order("submitted_at", { ascending: false })
    .range(offset, offset + limit - 1);

  if (startDate && endDate) {
    query = query.gte("submitted_at", startDate).lte("submitted_at", endDate);
  }

  const { data, count, error } = await query;

    if (error) {
      console.error("Get Form Responses Error:", error);
      return sendApiError(c, "Failed to fetch form responses", 500);
    }
    const transformedData = data.map((response) => ({
      response_id: response.id,
      form_id: response.form_id,
      answers: response.answers,
      submitted_at: response.submitted_at,
    }));
    
    return sendApiResponse(c, {
      responses: transformedData,
      total_count: count || 0
    });
    // return sendApiResponse(c, transformedData || []);
  } catch (err) {
    console.error("Unexpected Error in Fetching Form Responses:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

const deleteResponse = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const responseId = c.req.param("response_id");
    if (!responseId) {
      return sendApiError(c, "Response ID is required", 400);
    }

    const { data: response, error: responseError } = await supabase
      .from("automate_form_responses")
      .select("id, form_id")
      .eq("id", responseId)
      .single();

    if (responseError || !response) {
      return sendApiError(c, "Response not found", 404);
    }

    const { data: form, error: formError } = await supabase
      .from("automate_forms")
      .select("id, created_by")
      .eq("id", response.form_id)
      .single();

    if (formError || !form) {
      return sendApiError(c, "Form not found", 404);
    }

    if (form.created_by !== user.user.id) {
      return sendApiError(
        c,
        "You are not authorized to delete this response",
        403
      );
    }

    const { error: deleteError } = await supabase
      .from("automate_form_responses")
      .delete()
      .eq("id", responseId);

    if (deleteError) {
      console.error("Delete Response Error:", deleteError);
      return sendApiError(c, "Failed to delete response", 500);
    }

    return sendApiResponse(
      c,
      { message: "Response deleted successfully" },
      200
    );
  } catch (err) {
    console.error("Unexpected Error in Deleting Response:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

const exportFormResponses = async (c: Context) => {
  try {
    const user = c.get("user"); // Authenticate the user
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const formId = c.req.param("form_id");
    if (!formId) {
      return sendApiError(c, "Form ID is required", 400);
    }

    // Fetch form details and check ownership
    const { data: form, error: formError } = await supabase
      .from("automate_forms")
      .select("id, created_by")
      .eq("id", formId)
      .single();

    if (formError || !form) {
      return sendApiError(c, "Form not found", 404);
    }

    // Ensure only the creator can export responses
    if (form.created_by !== user.user.id) {
      return sendApiError(
        c,
        "You are not authorized to export this form's responses",
        403
      );
    }

    // Fetch responses from `form_responses` table
    const { data, error } = await supabase
      .from("automate_form_responses")
      .select("id, form_id, answers, submitted_at")
      .eq("form_id", formId)
      .order("submitted_at", { ascending: false });

    if (error) {
      console.error("Get Form Responses Error:", error);
      return sendApiError(c, "Failed to fetch form responses", 500);
    }

    if (!data || data.length === 0) {
      return sendApiError(c, "No responses available for export", 404);
    }

    // Extract unique column headers dynamically from responses
    let fieldNames = new Set<string>();

    data.forEach((response) => {
      response.answers.forEach((field: any) => {
        fieldNames.add(field.name);
      });
    });

    // Convert JSON responses to structured format for CSV
    const transformedData = data.map((response) => {
      let row: Record<string, any> = {
        response_id: response.id,
        form_id: response.form_id,
        submitted_at: response.submitted_at,
      };

      response.answers.forEach((field: any) => {
        if (typeof field.value === "object" && field.value !== null) {
          row[field.name] = Object.values(field.value).join(" "); // Merge objects (e.g., Full Name)
        } else {
          row[field.name] = field.value;
        }
      });

      return row;
    });

    // Convert JSON to CSV with dynamically extracted columns
    const csv = parse(transformedData, {
      fields: [
        "response_id",
        "form_id",
        "submitted_at",
        ...Array.from(fieldNames),
      ],
    });

    // Set response headers for CSV file download
    c.header("Content-Type", "text/csv");
    c.header(
      "Content-Disposition",
      `attachment; filename="form_responses_${formId}.csv"`
    );

    return c.body(csv);
  } catch (err) {
    console.error("Export Form Responses Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
const searchResponse = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const body = await c.req.json().catch(() => ({}));
    console.log("📢 Received form data:", body);

    const formId = body.form_id;
    const searchText = body.searchtext;
    const limit = body.limit || 10; // Default limit to 10
    const offset = body.offset || 0;
    if (!formId) {
      return sendApiError(c, "Form ID is required", 400);
    }

    // Call PostgreSQL function to search responses
    const { data, error } = await supabase.rpc("search_form_responses", {
      p_form_id: formId,
      p_search_text: searchText,
      p_limit: limit,
      p_offset: offset,
    });

    console.log("🔍 Search Result:", data);

    if (error) {
      console.error("❌ Search Responses Error:", error);
      return sendApiError(c, "Failed to search form responses", 500);
    }
    if (!data || data.length === 0) {
      return sendApiResponse(c, { responses: [], total_count: 0 });
    }
    const totalCount = data[0]?.total_count || 0;
   return sendApiResponse(c, {
      responses: data,
      total_count: totalCount,
    });
    // return sendApiResponse(c, data || []);
  } catch (err) {
    console.error("🔥 Unexpected Error in Searching Responses:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
const deleteMultiSelectResponse = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const body = await c.req.json()
    const responseIds = body.response_ids;

    if (!Array.isArray(responseIds) || responseIds.length === 0) {
      return sendApiError(c, "Response IDs array is required", 400);
    }

    // Fetch responses to verify ownership
    const { data: responses, error: fetchError } = await supabase
      .from("automate_form_responses")
      .select("id, form_id")
      .in("id", responseIds);

    if (fetchError || !responses || responses.length === 0) {
      return sendApiError(c, "Responses not found", 404);
    }

    // Fetch the corresponding forms
    const formIds = [...new Set(responses.map((res) => res.form_id))];
    const { data: forms, error: formError } = await supabase
      .from("automate_forms")
      .select("id, created_by")
      .in("id", formIds);

    if (formError || !forms || forms.length === 0) {
      return sendApiError(c, "Forms not found", 404);
    }

    // Check if the user is the owner of all related forms
    const userFormIds = forms.filter((form) => form.created_by === user.user.id).map((form) => form.id);
    const unauthorizedResponses = responses.filter((res) => !userFormIds.includes(res.form_id));

    if (unauthorizedResponses.length > 0) {
      return sendApiError(c, "You are not authorized to delete some of these responses", 403);
    }

    // Delete responses
    const { error: deleteError } = await supabase
      .from("automate_form_responses")
      .delete()
      .in("id", responseIds);

    if (deleteError) {
      console.error("Delete Multi-Select Response Error:", deleteError);
      return sendApiError(c, "Failed to delete responses", 500);
    }

    return sendApiResponse(c, { message: "Responses deleted successfully" }, 200);
  } catch (err) {
    console.error("Unexpected Error in Deleting Multiple Responses:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
const addsummaryColoumn = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const body = await c.req.json();
    const { form_response_id, prompt } = body;

    if (!form_response_id) {
      return sendApiError(c, "form_response_id is required", 400);
    }

    // Call the Supabase Edge Function to process the summary
    const { data, error } = await supabase.functions.invoke('process-form-analysis', {
      body: {
        form_response_id,
        prompt: prompt || "Please provide the summary of the response"
      }
    });

    if (error || !data?.success) {
      return sendApiError(c, data?.error || error?.message || "Failed to process summary", 500);
    }

    return sendApiResponse(c, {
      message: "Summary column added successfully",
      analysis: data.analysis,
      updated_response: data.updated_response,
    }, 200);

  } catch (err) {
    console.error("Error in addsummaryColoumn:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};


export { getResponse, deleteResponse, exportFormResponses, searchResponse,deleteMultiSelectResponse, addsummaryColoumn };
