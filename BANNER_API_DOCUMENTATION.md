# Banner Management API Documentation

## Overview
The Banner Management API provides CRUD operations for managing banners with file upload functionality. Banners are categorized and can be filtered, paginated, and grouped by category.

## Database Setup

### 1. Create the Banners Table
Run the SQL script located at `database/banners_table.sql` to create the banners table with proper indexes, RLS policies, and triggers.

```sql
-- The table includes:
-- - id (UUID, primary key)
-- - name (VARCHAR, banner name)
-- - category (VARCHAR, banner category)
-- - banner_url (TEXT, uploaded banner image URL)
-- - created_by (UUID, foreign key to auth.users)
-- - created_at (TIMESTAMP)
-- - updated_at (TIMESTAMP)
```

## API Endpoints

### Base URL: `/v1/banners`

All endpoints require authentication via the `verifySupabaseAuth` middleware.

### 1. Create Banner
**POST** `/v1/banners`

Creates a new banner with file upload.

**Request:**
- Content-Type: `multipart/form-data`
- Body:
  - `name` (string, required): Banner name
  - `category` (string, required): Banner category
  - `banner` (file, required): Banner image file

**Response:**
```json
{
  "success": true,
  "data": {
    "banner": {
      "id": "uuid",
      "name": "Banner Name",
      "category": "promotional",
      "banner_url": "https://storage-url/banner.jpg",
      "created_at": "2024-01-01T00:00:00Z",
      "created_by": "user-uuid"
    }
  }
}
```

### 2. Get All Banners
**GET** `/v1/banners`

Retrieves all banners with pagination and optional category filtering.

**Query Parameters:**
- `category` (string, optional): Filter by category
- `page` (number, optional, default: 1): Page number
- `limit` (number, optional, default: 10): Items per page

**Response:**
```json
{
  "success": true,
  "data": {
    "banners": [...],
    "pagination": {
      "current_page": 1,
      "total_pages": 5,
      "total_count": 50,
      "limit": 10
    }
  }
}
```

### 3. Get Banner by ID
**GET** `/v1/banners/:id`

Retrieves a specific banner by its ID.

**Response:**
```json
{
  "success": true,
  "data": {
    "banner": {
      "id": "uuid",
      "name": "Banner Name",
      "category": "promotional",
      "banner_url": "https://storage-url/banner.jpg",
      "created_at": "2024-01-01T00:00:00Z",
      "created_by": "user-uuid"
    }
  }
}
```

### 4. Update Banner
**PUT** `/v1/banners/:id`

Updates an existing banner. Only the banner creator or admin can update.

**Request:**
- Content-Type: `multipart/form-data`
- Body (all optional):
  - `name` (string): New banner name
  - `category` (string): New banner category
  - `banner` (file): New banner image file

**Response:**
```json
{
  "success": true,
  "data": {
    "banner": {
      "id": "uuid",
      "name": "Updated Banner Name",
      "category": "promotional",
      "banner_url": "https://storage-url/new-banner.jpg",
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-02T00:00:00Z",
      "created_by": "user-uuid"
    }
  }
}
```

### 5. Delete Banner
**DELETE** `/v1/banners/:id`

Deletes a banner. Only the banner creator or admin can delete.

**Response:**
```json
{
  "success": true,
  "data": {
    "message": "Banner deleted successfully"
  }
}
```

### 6. Get Banners Grouped by Category
**GET** `/v1/banners/grouped`

Retrieves all banners grouped by their categories.

**Response:**
```json
{
  "success": true,
  "data": {
    "banners_by_category": [
      {
        "category": "promotional",
        "banners": [...]
      },
      {
        "category": "seasonal",
        "banners": [...]
      }
    ]
  }
}
```

### 7. Get Banner Categories
**GET** `/v1/banners/categories`

Retrieves all unique banner categories.

**Response:**
```json
{
  "success": true,
  "data": {
    "categories": ["promotional", "seasonal", "welcome", "announcement"]
  }
}
```

## File Upload

- Files are uploaded to the `assets` bucket in Supabase Storage
- Supported file types: Images (jpg, jpeg, png, gif, webp)
- Files are automatically organized and given unique names
- Public URLs are generated for uploaded files

## Permissions

- **Read**: All authenticated users can view banners
- **Create**: All authenticated users can create banners
- **Update**: Only banner creators and admins can update banners
- **Delete**: Only banner creators and admins can delete banners

## Error Responses

All error responses follow this format:
```json
{
  "success": false,
  "error": "Error message",
  "status": 400
}
```

Common error codes:
- `400`: Bad Request (missing required fields)
- `401`: Unauthorized (not authenticated)
- `403`: Forbidden (insufficient permissions)
- `404`: Not Found (banner doesn't exist)
- `500`: Internal Server Error

## Usage Examples

### Create a Banner (cURL)
```bash
curl -X POST "http://localhost:3000/v1/banners" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "name=Summer Sale" \
  -F "category=promotional" \
  -F "banner=@/path/to/banner.jpg"
```

### Get Banners with Category Filter
```bash
curl -X GET "http://localhost:3000/v1/banners?category=promotional&page=1&limit=5" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Update a Banner
```bash
curl -X PUT "http://localhost:3000/v1/banners/banner-uuid" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "name=Updated Banner Name" \
  -F "banner=@/path/to/new-banner.jpg"
```
