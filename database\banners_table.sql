-- Create banners table
CREATE TABLE IF NOT EXISTS banners (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    url TEXT,
    title VARCHAR(255),
    description TEXT,
    banner_url TEXT NOT NULL,
    created_by UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Add foreign key constraint to users table if it exists
    CONSTRAINT fk_banners_created_by FOREIGN KEY (created_by) REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_banners_created_by ON banners(created_by);
CREATE INDEX IF NOT EXISTS idx_banners_created_at ON banners(created_at DESC);

-- Add RLS (Row Level Security) policies if needed
ALTER TABLE banners ENABLE ROW LEVEL SECURITY;

-- Policy to allow users to see all banners (read access)
CREATE POLICY "Allow read access to all banners" ON banners
    FOR SELECT USING (true);

-- Policy to allow users to insert their own banners
CREATE POLICY "Allow users to insert their own banners" ON banners
    FOR INSERT WITH CHECK (auth.uid() = created_by);

-- Policy to allow users to update their own banners
CREATE POLICY "Allow users to update their own banners" ON banners
    FOR UPDATE USING (auth.uid() = created_by);

-- Policy to allow users to delete their own banners
CREATE POLICY "Allow users to delete their own banners" ON banners
    FOR DELETE USING (auth.uid() = created_by);

-- Add trigger to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_banners_updated_at 
    BEFORE UPDATE ON banners 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Insert some sample data (optional)
INSERT INTO banners (name, url, title, description, banner_url, created_by) VALUES
('Summer Sale Banner', 'https://example.com/sale', 'Summer Sale', 'Get 50% off on all items', 'https://example.com/summer-sale.jpg', '00000000-0000-0000-0000-000000000000'),
('Welcome Banner', 'https://example.com/welcome', 'Welcome', 'Welcome to our store', 'https://example.com/welcome.jpg', '00000000-0000-0000-0000-000000000000'),
('Holiday Special', 'https://example.com/holiday', 'Holiday Special', 'Special holiday offers', 'https://example.com/holiday.jpg', '00000000-0000-0000-0000-000000000000')
ON CONFLICT DO NOTHING;
