import type { Context } from "hono";
import { supabase } from "../db";
import { sendApiError, sendApiResponse } from "../utils/Response";
import { upload } from "../utils/upload";
import { BUCKET_COMMON, BUCKET_FILE_UPLOAD } from "../constant";
import { mergeFieldsWithAnswers } from "../utils/comman";
import { UsageService } from "../services/usage.service";
import { parseUserAgent } from "../utils/userAgentParser";

const SubmitForm = async (c: Context) => {
  try {
    if (!c.req.json) {
      return sendApiError(c, "Invalid request body", 400);
    }
    const body = await c.req.json();
     const allHeaders = {
          'x-forwarded-for': c.req.header('x-forwarded-for'),
          'x-real-ip': c.req.header('x-real-ip'),
          'cf-connecting-ip': c.req.header('cf-connecting-ip'),
          'user-agent': c.req.header('user-agent'),
          'host': c.req.header('host'),
          'origin': c.req.header('origin')
        };
        console.log("All request headers:", allHeaders);
    
        // Get IP address with enhanced fallback handling
        let ip_address = 'unknown';
        const forwarded = c.req.header('x-forwarded-for');
        const realIp = c.req.header('x-real-ip');
        const cfIp = c.req.header('cf-connecting-ip');
        // Safely get remote address if available
        let remoteAddr: string | undefined = undefined;
        const raw = c.req.raw as { socket?: { remoteAddress?: string } };
        if (raw.socket && typeof raw.socket.remoteAddress === 'string') {
          remoteAddr = raw.socket.remoteAddress;
        }
    
        // Try different methods to get IP
        if (forwarded) {
          ip_address = forwarded.split(',')[0].trim();
        } else if (realIp) {
          ip_address = realIp;
        } else if (cfIp) {
          ip_address = cfIp;
        } else if (remoteAddr) {
          // Clean up IPv6 format if present
          ip_address = remoteAddr.replace(/^::ffff:/, '').split(':')[0];
        }
    
        // Clean up the IP address
        if (ip_address !== 'unknown') {
          ip_address = ip_address.replace(/^::ffff:/, '');
          ip_address = ip_address.split(':')[0];
        }
    
        console.log("Detected IP address:", ip_address);
    
        // Parse user agent string and browser/device info
        const userAgentString = c.req.header('user-agent') || '';
        const { browser, device } = parseUserAgent(userAgentString);
        console.log("details",browser,device)
    
    const { form_id, answers } = body;
    if (!form_id) {
      return sendApiError(c, "form_id is required", 400);
    }
    if (!answers) {
      return sendApiError(c, "answers are required", 400);
    }
    const { data: existingForm, error: formError } = await supabase
      .from("automate_forms")
      .select("id, published, created_by, workspace_id, automate_form_settings(accept_responses,copy_allowed)")
      .eq("id", form_id)
      .eq("is_deleted",false)
      .single();

    if (formError || !existingForm) {
      return sendApiError(c, "Form not found", 404);
    }

    if (!existingForm.published) {
      return sendApiError(
        c,
        "Form is not published, can't submit responses",
        403
      );
    }

    // Check if responses are allowed
    if (!existingForm.automate_form_settings[0]?.accept_responses) {
      return sendApiError(
        c,
        "Form is not accepting responses at the moment",
        403
      );
    }

    // Check if submission limit has been reached
    if (existingForm.workspace_id) {
      const usageData = await UsageService.getWorkspaceUsage(existingForm.workspace_id);
      if (usageData) {
        // Check if this submission would exceed the monthly limit
        if (usageData.usage.submissions_count >= usageData.limits.monthly_submissions_limit) {
          return sendApiError(
            c,
            "Monthly submission limit reached. Please upgrade your plan to accept more submissions.",
            403
          );
        }
      }
    }

    // Insert form response
    const { data, error } = await supabase
      .from("automate_form_responses")
      .insert({
        form_id,
        answers,
        submitted_at: new Date().toISOString(),
        workspace_id: existingForm.workspace_id,
        ip_address: ip_address,
        user_agent: userAgentString, // Store full user agent string
        browser: browser,
        device: device
      })
      .select();

    // Track submission in workspace usage
    if (existingForm.workspace_id) {
      await UsageService.incrementSubmissionCount(existingForm.workspace_id);
    }

    if (error) {
      console.error("Error inserting form response:", error);
      return sendApiError(c, "Failed to submit form", 500);
    }
    const { data: formfield, error: formfielderror } = await supabase
      .from("automate_form_fields")
      .select("fields")
      .eq("form_id", form_id)
      .single();
    if (formfielderror || !formfield) {
      return c.json({ error: "Form filed not found" }, 404);
    }

    let mappedData;
    try {
      mappedData = mergeFieldsWithAnswers(formfield.fields, answers);
    } catch (mergeError) {
      console.error("Error merging form fields with answers:", mergeError);
      return sendApiError(c, "Failed to process form responses", 500);
    }

    return sendApiResponse(c, mappedData, 201);
  } catch (err) {
    console.error("Unexpected error while submitting form:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

const uploadfile = async (c: Context) => {
  const body = await c.req.parseBody();
  const file = body["upload"] as File | undefined;
  const workspaceId = body["workspace_id"] as string | undefined;

  if (!file) {
    return sendApiError(c, "file & workspace_id is required", 400);
  }

  try {
    const targetBucket = workspaceId ? BUCKET_FILE_UPLOAD : BUCKET_COMMON;
    let wsId: number | undefined = undefined;
    if (workspaceId) {
      wsId = parseInt(workspaceId);
      if (wsId) {
        const usageData = await UsageService.getWorkspaceUsage(wsId);
        if (usageData) {
          const fileSizeMB = file.size / (1024 * 1024);
          const newTotalUsage = usageData.usage.storage_used_mb + fileSizeMB;

          // Check if new total usage exceeds the storage limit
          if (newTotalUsage > usageData.limits.storage_limit_mb) {
            return sendApiError(
              c,
              "Storage limit reached. Please upgrade your plan to get more storage space.",
              403
            );
          }
        }
      }
    }
    const publicurl = await upload(file, targetBucket, wsId);
    console.log("publicurl", publicurl);

    return sendApiResponse(c, {
      message: "File uploaded successfully",
      fileUrl: publicurl,
    });
  } catch (err) {
    console.error("Upload Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

const removeFile = async (c: Context) => {
  try {
    const body = await c.req.json();
    const { fileUrl, workspaceId } = body;

    if (!fileUrl) {
      return sendApiError(c, "fileUrl is required", 400);
    }

    // Extract the file path from the URL
    const filePath = fileUrl.split('/').pop();
    if (!filePath) {
      return sendApiError(c, "Invalid file URL", 400);
    }

    const targetBucket = workspaceId ? BUCKET_FILE_UPLOAD : BUCKET_COMMON;
    let wsId: number | undefined = undefined;

    if (workspaceId) {
      wsId = parseInt(workspaceId);
      if (isNaN(wsId)) {
        return sendApiError(c, "Invalid workspace ID", 400);
      }
    }

    // Delete the file from storage
    const { error: deleteError } = await supabase.storage
      .from(targetBucket)
      .remove([filePath]);

    if (deleteError) {
      console.error("Error deleting file:", deleteError);
      return sendApiError(c, "Failed to delete file", 500);
    }

    // If it's a workspace file, update the storage usage
    if (wsId) {
      const usageData = await UsageService.getWorkspaceUsage(wsId);
      if (usageData) {
        // Get file size before deletion
        const { data: fileData } = await supabase.storage
          .from(targetBucket)
          .getPublicUrl(filePath);
        
        if (fileData) {
          // Update storage usage (you might need to implement this method in UsageService)
          await UsageService.decrementStorageUsage(wsId, fileData.size);
          console.log('fileData',fileData)
        }
      }
    }

    return sendApiResponse(c, {
      message: "File deleted successfully"
    });
  } catch (err) {
    console.error("Delete Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

export { SubmitForm, uploadfile, removeFile };


