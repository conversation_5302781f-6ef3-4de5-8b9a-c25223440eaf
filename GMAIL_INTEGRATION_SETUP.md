# Gmail Integration Setup Guide

## Overview
This guide explains how to set up and use the Gmail integration for AutomateForm.ai to send emails to form respondents automatically.

## Features
- **Automatic Email Sending**: Send emails to form respondents when they submit forms
- **Custom Email Templates**: Create personalized email templates with form data
- **Multiple Actions**: Confirmation emails, notifications, and custom templates
- **OAuth 2.0 Authentication**: Secure Gmail integration using Google OAuth
- **Template Variables**: Use form field data in email content

## Database Setup

### 1. Add Gmail Integration to Database

```sql
-- Add Gmail integration to automate_form_integrations table
INSERT INTO automate_form_integrations (id, name, status, description, icon) 
VALUES (
  'f5732279-8247-49a2-8453-************', -- Use this exact ID (already configured in scopes)
  'Gmail', 
  'active', 
  'Send emails to form respondents via Gmail', 
  'https://cdn.jsdelivr.net/npm/simple-icons@v9/icons/gmail.svg'
);

-- Add Gmail actions to automate_form_integration_actions table
INSERT INTO automate_form_integration_actions (id, integration_id, name, description) 
VALUES 
(
  'gmail-send-confirmation-id', -- Generate a UUID
  'f5732279-8247-49a2-8453-************',
  'Send Confirmation Email', 
  'Send confirmation email to form respondent'
),
(
  'gmail-send-notification-id', -- Generate a UUID
  'f5732279-8247-49a2-8453-************',
  'Send Notification Email', 
  'Send notification email about form submission'
),
(
  'gmail-send-custom-id', -- Generate a UUID
  'f5732279-8247-49a2-8453-************',
  'Send Custom Email', 
  'Send custom email using template'
);
```

### 2. Update Webhook Controller

Update the `GMAIL_INTEGRATION_ID` constant in `src/controllers/webhook.controllers.ts`:

```typescript
const GMAIL_INTEGRATION_ID = "f5732279-8247-49a2-8453-************";
```

## Google Cloud Console Setup

### 1. Enable Gmail API

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project (or create a new one)
3. Go to "APIs & Services" > "Library"
4. Search for "Gmail API" and enable it

### 2. Configure OAuth Consent Screen

1. Go to "APIs & Services" > "OAuth consent screen"
2. Choose "External" user type
3. Fill in required information:
   - App name: "AutomateForm.ai"
   - User support email: Your email
   - Developer contact information: Your email
4. Add scopes:
   - `https://www.googleapis.com/auth/gmail.send`
   - `https://www.googleapis.com/auth/userinfo.email`
   - `https://www.googleapis.com/auth/userinfo.profile`

### 3. Create OAuth 2.0 Credentials

1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth 2.0 Client IDs"
3. Choose "Web application"
4. Add authorized redirect URIs:
   - Development: `http://localhost:8000/auth/google/callback`
   - Production: `https://your-domain.com/auth/google/callback`

## API Endpoints

### Authentication
- `GET /auth/google` - Initiate OAuth flow (existing)
- `GET /auth/google/callback` - Handle OAuth callback (existing)

### Integration Management
- `GET /v1/gmail/action/:id` - Get available actions
- `GET /v1/gmail/connection/:id` - Get user credentials

### Gmail Features
- `POST /v1/gmail/profile` - Get Gmail profile
- `POST /v1/gmail/test` - Send test email
- `POST /v1/gmail/linkform` - Link form to Gmail

### Webhooks
- `POST /v1/webhook/sendEmailToRespondent` - Handle form submissions

## Usage Flow

### 1. Connect Gmail Account

Frontend should redirect user to:
```
GET /auth/google?integration_id=f5732279-8247-49a2-8453-************&name=Gmail Connection&redirect_uri={frontend_url}&formId={form_id}&formType=integration&action_id={action_id}
```

### 2. Test Email Sending

```javascript
POST /v1/gmail/test
{
  "credentialId": "credential-uuid",
  "testEmail": "<EMAIL>",
  "subject": "Test Email",
  "body": "<h1>Test Email</h1><p>This is a test email.</p>"
}
```

### 3. Link Form to Gmail

```javascript
POST /v1/gmail/linkform
{
  "form_id": "form-uuid",
  "integration_id": "f5732279-8247-49a2-8453-************",
  "credential_id": "credential-uuid",
  "action_id": "gmail-send-confirmation-id",
  "email_template": "Thank you {{name}} for your submission!",
  "subject_template": "Thank you for contacting us",
  "sender_name": "AutomateForm Team",
  "column_mapped_data": [
    {
      "id": "field1",
      "title": "Name",
      "key": "{{field_id.value}}"
    }
  ]
}
```

## Email Templates

### Template Variables

You can use these variables in your email templates:

#### Form Variables
- `{{form_title}}` - Form title
- `{{submitted_at}}` - Full submission timestamp
- `{{submission_date}}` - Submission date only
- `{{submission_time}}` - Submission time only

#### Field Variables
- `{{field_id.value}}` - Field value
- `{{field_id.fullname}}` - Full name for name fields
- `{{field_id.value.firstName}}` - Nested object properties

### Example Templates

#### Confirmation Email Template
```html
<!DOCTYPE html>
<html>
<head>
    <style>
        body { font-family: Arial, sans-serif; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #f8f9fa; padding: 20px; border-radius: 8px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>Thank you, {{name}}!</h2>
            <p>We have received your submission for {{form_title}}</p>
        </div>
        <p>Your message: {{message}}</p>
        <p>We will get back to you soon at {{email}}</p>
        <p>Submitted on: {{submitted_at}}</p>
    </div>
</body>
</html>
```

#### Subject Template
```
Thank you for contacting us, {{name}}!
```

## Email Field Detection

The system automatically detects email fields in forms using these patterns:
- Field type: `email`
- Field name contains: `email`, `e-mail`
- Field title contains: `email`, `e-mail`

Make sure your forms have an email field for the integration to work.

## Actions Explained

### 1. Send Confirmation Email
- **Purpose**: Send thank you/confirmation email to form respondent
- **Recipient**: Form respondent (extracted from email field)
- **Template**: Uses custom template or default confirmation template

### 2. Send Notification Email
- **Purpose**: Send notification about new submission
- **Recipient**: Form respondent
- **Template**: Simple notification template

### 3. Send Custom Email
- **Purpose**: Send completely custom email using templates
- **Recipient**: Form respondent
- **Template**: Fully customizable template with variables

## Error Handling

All webhook executions are logged in `automate_form_webhook_execution_logs` table:
- `status`: 'pending', 'success', 'error'
- `response`: Success message or error details
- `data_sent`: Original form submission data

## Testing

### 1. Test Email Sending
```javascript
// Test basic email functionality
POST /v1/gmail/test
{
  "credentialId": "your-credential-id",
  "testEmail": "<EMAIL>",
  "subject": "Test Email",
  "body": "This is a test email from Gmail integration"
}
```

### 2. Test Form Submission
1. Create a test form with an email field
2. Connect Gmail integration
3. Submit the form
4. Check recipient's email inbox

## Troubleshooting

### Common Issues

1. **"No email field found in form submission"**
   - Ensure form has an email field
   - Check field naming conventions

2. **"Failed to authenticate with Gmail"**
   - Re-authenticate Gmail account
   - Check OAuth credentials

3. **"Failed to send email"**
   - Check Gmail API quotas
   - Verify sender email permissions
   - Check email content for spam triggers

### Debug Steps

1. Check webhook execution logs:
```sql
SELECT * FROM automate_form_webhook_execution_logs 
WHERE status = 'error' 
ORDER BY created_at DESC;
```

2. Verify integration setup:
```sql
SELECT * FROM form_integrations 
WHERE integration_id = 'f5732279-8247-49a2-8453-************';
```

3. Test Gmail API access:
```javascript
POST /v1/gmail/profile
{
  "credentialId": "your-credential-id"
}
```

## Security Considerations

1. **OAuth 2.0**: Secure authentication flow
2. **Token Storage**: Encrypted storage in database
3. **Scope Limitation**: Only necessary Gmail permissions
4. **Email Validation**: Automatic email field detection
5. **Rate Limiting**: Respect Gmail API limits

## Next Steps

1. Set up database entries for Gmail integration
2. Configure Google Cloud Console
3. Test OAuth flow and email sending
4. Create email templates
5. Deploy and monitor webhook executions

For additional support, check the Gmail API documentation: https://developers.google.com/gmail/api
