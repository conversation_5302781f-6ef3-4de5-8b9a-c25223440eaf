# Slack Integration Setup Guide

## Overview
This guide explains how to set up and use the Slack integration for AutomateForm.ai, following the same patterns as existing integrations (Google Sheets, WhatsApp).

## Database Setup

### 1. Add Slack Integration to Database

First, you need to add the Slack integration entry to your database:

```sql
-- Add Slack integration to automate_form_integrations table
INSERT INTO automate_form_integrations (id, name, status, description, icon) 
VALUES (
  'your-slack-integration-id', -- Generate a UUID
  'Slack', 
  'active', 
  'Send form submissions to Slack channels', 
  'slack-icon-url'
);

-- Add Slack actions to automate_form_integration_actions table
INSERT INTO automate_form_integration_actions (id, integration_id, name, description) 
VALUES (
  'slack-send-message-action-id', -- Generate a UUID
  'your-slack-integration-id', -- Same as above
  'Send Message', 
  'Send form submission as a message to Slack channel'
);
```

### 2. Update Webhook Controller

Update the `SLACK_INTEGRATION_ID` constant in `src/controllers/webhook.controllers.ts`:

```typescript
const SLACK_INTEGRATION_ID = "your-slack-integration-id"; // Replace with your actual ID
```

## Slack App Configuration

### 1. Create a Slack App

1. Go to [Slack API](https://api.slack.com/apps)
2. Click "Create New App"
3. Choose "From scratch"
4. Enter app name (e.g., "AutomateForm.ai")
5. Select your workspace

### 2. Configure OAuth & Permissions

1. Go to "OAuth & Permissions" in your app settings
2. Add the following **Bot Token Scopes**:
   - `channels:read` - View basic information about public channels
   - `groups:read` - View basic information about private channels
   - `chat:write` - Send messages as the app
   - `users:read` - View people in the workspace
   - `team:read` - View basic information about the workspace

3. Add **Redirect URLs**:
   - Development: `http://localhost:8000/v1/slack/callback`
   - Production: `https://your-domain.com/v1/slack/callback`

### 3. Get App Credentials

From the "Basic Information" page, copy:
- **Client ID**
- **Client Secret**

Update your `.env` file:
```env
SLACK_CLIENT_ID=your_slack_client_id_here
SLACK_CLIENT_SECRET=your_slack_client_secret_here
SLACK_REDIRECT_URI=http://localhost:8000/v1/slack/callback
```

## API Endpoints

### Authentication
- `GET /v1/slack/addconnection` - Initiate OAuth flow
- `GET /v1/slack/callback` - Handle OAuth callback

### Integration Management
- `GET /v1/slack/action/:id` - Get available actions
- `GET /v1/slack/connection/:id` - Get user credentials
- `PUT /v1/slack/updateconnection` - Update connection

### Slack Resources
- `POST /v1/slack/channels` - Get workspace channels
- `POST /v1/slack/users` - Get workspace users
- `POST /v1/slack/linkform` - Link form to Slack

### Webhooks
- `POST /v1/webhook/sendDataSlack` - Handle form submissions

## Usage Flow

### 1. Connect Slack Workspace

Frontend should redirect user to:
```
GET /v1/slack/addconnection?integration_id={id}&name={connection_name}&redirect_uri={frontend_url}
```

### 2. Get Available Channels

```javascript
POST /v1/slack/channels
{
  "credentialId": "credential-uuid"
}
```

Response:
```javascript
{
  "message": "Slack channels retrieved successfully",
  "data": [
    {
      "id": "C1234567890",
      "name": "general",
      "is_private": false,
      "is_member": true
    }
  ]
}
```

### 3. Link Form to Slack

```javascript
POST /v1/slack/linkform
{
  "form_id": "form-uuid",
  "integration_id": "slack-integration-id",
  "credential_id": "credential-uuid",
  "action_id": "slack-send-message-action-id",
  "channel_id": "C1234567890",
  "channel_name": "general",
  "column_mapped_data": [
    {
      "id": "field1",
      "title": "Name",
      "key": "{{field_id.value}}"
    }
  ]
}
```

### 4. Form Submission Webhook

When a form is submitted, the system will automatically send a message to the configured Slack channel.

## Message Formatting

### Default Format (No Mapping)
Creates rich message blocks with form fields displayed in a structured format.

### Custom Format (With Mapping)
Uses the `column_mapped_data` to create custom messages with template variables:
- `{{field_id.value}}` - Field value
- `{{field_id.fullname}}` - Full name for name fields
- `{{field_id.value.firstName}}` - Nested object properties

## Error Handling

All webhook executions are logged in `automate_form_webhook_execution_logs` table with:
- `status`: 'pending', 'success', 'error'
- `response`: Success message or error details
- `data_sent`: Original form submission data

## Testing

1. Create a test form
2. Connect Slack workspace
3. Link form to a test channel
4. Submit the form
5. Check the Slack channel for the message

## Security Considerations

1. **Token Storage**: Slack tokens are stored encrypted in the database
2. **Permissions**: Users can only access their own credentials
3. **Workspace Isolation**: Integrations are isolated by workspace
4. **OAuth Flow**: Secure OAuth 2.0 flow with state validation

## Troubleshooting

### Common Issues

1. **"User has not authenticated Slack"**
   - User needs to complete OAuth flow
   - Check if credentials exist in database

2. **"No Slack channel configured"**
   - Form integration missing channel_id in metadata
   - Re-link the form to Slack

3. **"Failed to send message to Slack"**
   - Check bot permissions in Slack workspace
   - Verify channel exists and bot has access
   - Check Slack API rate limits

### Debug Steps

1. Check webhook execution logs:
```sql
SELECT * FROM automate_form_webhook_execution_logs 
WHERE status = 'error' 
ORDER BY created_at DESC;
```

2. Verify integration setup:
```sql
SELECT * FROM form_integrations 
WHERE integration_id = 'your-slack-integration-id';
```

3. Check credentials:
```sql
SELECT id, name, auth_type, enabled 
FROM automate_form_integration_credentials 
WHERE integration_id = 'your-slack-integration-id';
```

## Next Steps

1. Set up database entries for Slack integration
2. Configure Slack app with proper permissions
3. Update environment variables
4. Test the integration flow
5. Deploy and monitor webhook executions

For additional support, check the Slack API documentation: https://api.slack.com/
