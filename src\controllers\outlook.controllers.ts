import dotenv from "dotenv";
dotenv.config({ path: "./.env" });

import type { Context } from "hono";
import { supabase } from "../db";
import { sendApiError, sendApiResponse } from "../utils/Response";
import {
  getValidOutlookToken,
  getOutlookProfile,
  createDefaultEmailTemplate,
  exchangeCodeForTokens
} from "../utils/outlookHelper";

const OUTLOOK_CLIENT_ID = process.env.OUTLOOK_CLIENT_ID;
const OUTLOOK_CLIENT_SECRET = process.env.OUTLOOK_CLIENT_SECRET;
const OUTLOOK_REDIRECT_URI = process.env.OUTLOOK_REDIRECT_URI;

/**
 * Get actions for Outlook integration
 */
const getActionforOutlook = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const integrationId = c.req.param("id");
    if (!integrationId) {
      return sendApiError(c, "Integration ID is required", 400);
    }

    // Get actions for Outlook integration
    const { data: actions, error } = await supabase
      .from("automate_form_integration_actions")
      .select("id, name, description")
      .eq("integration_id", integrationId);

    if (error) {
      console.error("Get Actions Error:", error);
      return sendApiError(c, "Failed to retrieve actions", 500);
    }

    return sendApiResponse(c, actions || []);
  } catch (err) {
    console.error("Get Actions Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

/**
 * Get user's Outlook integration credentials
 */
const getUserIntegrationCredentials = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const integrationId = c.req.param("id");
    if (!integrationId) {
      return sendApiError(c, "Integration ID is required", 400);
    }

    // Get user's Outlook credentials
    const { data: credentials, error } = await supabase
      .from("automate_form_integration_credentials")
      .select("id, name, auth_data, created_at")
      .eq("integration_id", integrationId);

      console.log("console",credentials)
    if (error) {
      console.error("Get Credentials Error:", error);
      return sendApiError(c, "Failed to retrieve credentials", 500);
    }

    // Remove sensitive data before sending
    const sanitizedCredentials = credentials?.map(cred => ({
      id: cred.id,
      name: cred.name,
      email: cred.auth_data?.email || 'Unknown',
      created_at: cred.created_at
    })) || [];

    return sendApiResponse(c, sanitizedCredentials);
  } catch (err) {
    console.error("Get Credentials Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

/**
 * Add Outlook connection (OAuth flow)
 */
const addOutlookConnection = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const { integration_id, name, redirect_uri, formId, formType } = c.req.query();
    
    if (!integration_id || !name || !redirect_uri || !formId || !formType) {
      return sendApiError(c, "Missing required parameters", 400);
    }

    // Validate integration
    const { data: integration, error: integrationError } = await supabase
      .from("automate_form_integrations")
      .select("name")
      .eq("id", integration_id)
      .single();

    if (integrationError || !integration) {
      console.error("Integration Error:", integrationError);
      return sendApiError(c, "Failed to retrieve integration", 500);
    }

    // Generate Microsoft OAuth URL for Outlook
    const scopes = [
      'https://graph.microsoft.com/Mail.Send',
      'https://graph.microsoft.com/User.Read'
    ];

    const state = JSON.stringify({
      userId: user.user.id,
      integration_id,
      name,
      redirect_uri,
      integration_type: 'outlook',
      formId,
      formType,
    });

    const authUrl = `https://login.microsoftonline.com/common/oauth2/v2.0/authorize?` +
      `client_id=${OUTLOOK_CLIENT_ID}&` +
      `response_type=code&` +
      `redirect_uri=${encodeURIComponent(OUTLOOK_REDIRECT_URI || '')}&` +
      `scope=${encodeURIComponent(scopes.join(' '))}&` +
      `state=${encodeURIComponent(state)}&` +
      `response_mode=query`;

    return sendApiResponse(c, { authUrl });
  } catch (err) {
    console.error("Outlook Auth Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

/**
 * Get Outlook profile
 */
const getProfile = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const { credentialId } = await c.req.json();

    if (!credentialId) {
      return sendApiError(c, "Credential ID is required", 400);
    }

    const profile = await getOutlookProfile(credentialId);

    if (!profile) {
      return sendApiError(c, "Failed to get Outlook profile", 500);
    }

    return sendApiResponse(c, profile);
  } catch (err) {
    console.error("Get Profile Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

/**
 * Test email sending
 */
const testEmail = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const { credentialId, testEmail, subject, body } = await c.req.json();

    if (!credentialId || !testEmail) {
      return sendApiError(c, "Missing required parameters", 400);
    }

    // Get the valid Outlook token
    const outlookToken = await getValidOutlookToken(credentialId);

    if (!outlookToken) {
      return sendApiError(c, "User has not authenticated Outlook", 403);
    }

    // Get sender email from credentials
    const { data: credential } = await supabase
      .from('automate_form_integration_credentials')
      .select('auth_data')
      .eq('id', credentialId)
      .single();

    const senderEmail = credential?.auth_data?.email || '<EMAIL>';

    // Create test email template
    const testSubject = subject || 'Test Email from AutomateForm.ai';
    const testBody = body || `
      <h2>Test Email</h2>
      <p>This is a test email from your Outlook integration.</p>
      <p>If you received this email, your Outlook integration is working correctly!</p>
      <p>Sent at: ${new Date().toLocaleString()}</p>
    `;

    // Send test email
    const { sendOutlookMessage } = await import('../utils/outlookHelper');
    const success = await sendOutlookMessage(
      outlookToken,
      testEmail,
      senderEmail,
      testSubject,
      testBody,
      true
    );

    if (!success) {
      return sendApiError(c, "Failed to send test email", 500);
    }

    return sendApiResponse(c, { message: "Test email sent successfully" });
  } catch (err) {
    console.error("Test Email Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

/**
 * Link form to Outlook
 */
const linkFormToOutlook = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) return sendApiError(c, "Unauthorized", 401);

    let body;
    try {
      body = await c.req.json();
    } catch {
      return sendApiError(c, "Invalid JSON body", 400);
    }

    const { 
      form_id, 
      integration_id, 
      credential_id, 
      action_id, 
      column_mapped_data
    } = body;

    if (!form_id || !integration_id || !credential_id || !action_id) {
      return sendApiError(c, "Missing required parameters", 400);
    }

    const userId = user.user.id;

    // Check if integration already exists
    const { data: existingIntegration, error: checkError } = await supabase
      .from("form_integrations")
      .select("id")
      .eq("form_id", form_id)
      .eq("integration_id", integration_id)
      .eq("credential_id", credential_id)
      .single();

    if (existingIntegration) {
      // Update existing integration
      const { error: updateError } = await supabase
        .from("form_integrations")
        .update({
          action_id,
          mapped_data: column_mapped_data || null,
          updated_at: new Date().toISOString()
        })
        .eq("id", existingIntegration.id);

      if (updateError) {
        console.error("DB Update Error:", updateError);
        return sendApiError(c, "Failed to update form integration", 500);
      }

      return sendApiResponse(c, { message: "Form integration updated successfully" }, 200);
    }

    // Insert new integration if it doesn't exist
    const { error } = await supabase
      .from("form_integrations")
      .insert({
        form_id,
        integration_id,
        credential_id,
        action_id,
        created_by: userId,
        metadata: null,
        mapped_data: column_mapped_data || null,
        enabled: true
      });

    if (error) {
      console.error("DB Insert Error:", error);
      return sendApiError(c, "Failed to save form integration", 500);
    }

    return sendApiResponse(c, { message: "Form integration linked successfully" }, 200);
  } catch (err) {
    console.error("LinkFormToGmail Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

/**
 * Handle Outlook OAuth callback
 */
const handleOutlookCallback = async (c: Context) => {
  try {
    const { code, state } = c.req.query();
    if (!code || !state) {
      return sendApiError(c, "Missing code or state", 400);
    }

    const { userId, redirect_uri, formId, formType, integration_id, name, integration_type } = JSON.parse(state);

    const cleanRedirectUri = redirect_uri.replace(/^['"]+|['"]+$/g, '');
    const failRedirectUrl = `${cleanRedirectUri}?formId=${encodeURIComponent(formId)}&formType=${encodeURIComponent(formType)}&success=false`;

    if (!userId || !cleanRedirectUri) {
      return c.redirect(failRedirectUrl);
    }
console.log("code",code)

    // Exchange code for tokens
    const tokens = await exchangeCodeForTokens(code);
    console.log("token",tokens)
// console.log("toekn",tokens)
    if (!tokens || !tokens.access_token) {
      console.error("Failed to get tokens from Outlook");
      return c.redirect(failRedirectUrl);
    }

    // Get user info from Microsoft Graph using access token directly
    const response = await fetch('https://graph.microsoft.com/v1.0/me', {
      headers: {
        'Authorization': `Bearer ${tokens.access_token}`,
        'Content-Type': 'application/json'
      }
    });

    const userProfile = response.ok ? await response.json() : null;

    if (!userProfile || !userProfile.mail) {
      console.error("Failed to get user profile from Outlook");
      return c.redirect(failRedirectUrl);
    }

    // Fetch user from database
    const { data: user, error: userError } = await supabase
      .from("user_profile")
      .select("id, email, workspace_id")
      .eq("id", userId)
      .single();

    if (userError || !user) {
      console.error("Database error or user not found:", userError);
      return c.redirect(failRedirectUrl);
    }

    // Insert credentials
    const { data: insertedData, error: insertError } = await supabase
      .from("automate_form_integration_credentials")
      .insert({
        name: name,
        created_by: userId,
        workspace_id: user.workspace_id || null,
        integration_id,
        auth_type: "OAuth2",
        auth_data: {
          access_token: tokens.access_token,
          refresh_token: tokens.refresh_token || null,
          expires_at: new Date(Date.now() + tokens.expires_in * 1000).toISOString(),
          email: userProfile.mail,
          name: userProfile.displayName || null,
        },
        created_at: new Date().toISOString(),
      })
      .select("id")
      .single();

    if (insertError || !insertedData) {
      console.error("Error inserting credentials:", insertError);
      return c.redirect(failRedirectUrl);
    }

    const credentialId = insertedData.id;
    const finalRedirectUrl = `${cleanRedirectUri}?formId=${encodeURIComponent(formId)}&formType=${encodeURIComponent(formType)}&integration_type=${integration_type}&success=true&credential_id=${credentialId}&integration_id=${integration_id}`;

    console.log("Redirecting to:", finalRedirectUrl);
    return c.redirect(finalRedirectUrl);
  } catch (err) {
    console.error("Outlook Auth Callback Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

export {
  getActionforOutlook,
  getUserIntegrationCredentials,
  addOutlookConnection,
  getProfile,
  testEmail,
  linkFormToOutlook,
  handleOutlookCallback
};
