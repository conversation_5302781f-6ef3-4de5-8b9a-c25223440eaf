
import { Hono } from "hono";
const webhook = new Hono();
import { sendDataToGoogleSheet, sendDataToSlack, sendEmailToRespondent, sendOutlookEmailToRespondent } from "../controllers/webhook.controllers";
webhook.post("/sendDataSheet", sendDataToGoogleSheet);
webhook.post("/sendDataSlack", sendDataToSlack);
webhook.post("/sendEmailToRespondent", sendEmailToRespondent);
webhook.post("/sendOutlookEmailToRespondent", sendOutlookEmailToRespondent);
export {webhook};
