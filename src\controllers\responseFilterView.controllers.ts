import { Context } from "hono";
import { supabase } from "../db";
import { sendApiError, sendApiResponse } from "../utils/Response";

interface FilterViewData {
  columns?: string[];
  filters?: {
    field: string;
    operator: string;
    value: any;
  }[];
  sorting?: {
    field: string;
    direction: 'asc' | 'desc';
  }[];
}

interface CreateViewRequest {
  name: string;
  description?: string;
  data: FilterViewData;
  access?: string[]; // Array of user UUIDs who can access this view
}

/**
 * Create a new response filter view
 */
const createFilterView = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const body: CreateViewRequest = await c.req.json();
    const { name, description, data, access,form_id } = body;

    if (!name || !data || !form_id) {
      return sendApiError(c, "Name and filter data are required", 400);
    }

    // // Validate filter data structure
    // if (!validateFilterData(data)) {
    //   return sendApiError(c, "Invalid filter data structure", 400);
    // }

    const { data: view, error } = await supabase
      .from("automate_form_response_filter_view")
      .insert({
        name,
        description,
        data,
        access: access || [],
        form_id,
        created_by: user.user.id,
      })
      .select()
      .single();

    if (error) {
      console.error("Error creating filter view:", error);
      return sendApiError(c, "Failed to create filter view", 500);
    }

    return sendApiResponse(c, {
      message: "Filter view created successfully",
      data: view,
    });
  } catch (err) {
    console.error("Create filter view error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

/**
 * Update an existing filter view
 */
const updateFilterView = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const viewId = c.req.param("id");
    if (!viewId) {
      return sendApiError(c, "View ID is required", 400);
    }

    const body: Partial<CreateViewRequest> = await c.req.json();
    const { name, description, data, access } = body;

    // Check if view exists and user has permission
    const { data: existingView, error: fetchError } = await supabase
      .from("automate_form_response_filter_view")
      .select("created_by, access")
      .eq("id", viewId)
      .single();

    if (fetchError || !existingView) {
      return sendApiError(c, "Filter view not found", 404);
    }

    // Check if user has permission to update
    if (existingView.created_by !== user.user.id && 
        !existingView.access.includes(user.user.id)) {
      return sendApiError(c, "You don't have permission to update this view", 403);
    }

    // // Validate new filter data if provided
    // if (data && !validateFilterData(data)) {
    //   return sendApiError(c, "Invalid filter data structure", 400);
    // }

    const updateData: any = {
      updated_at: new Date().toISOString(),
    };

    if (name) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (data) updateData.data = data;
    if (access) updateData.access = access;

    const { data: updatedView, error } = await supabase
      .from("automate_form_response_filter_view")
      .update(updateData)
      .eq("id", viewId)
      .select()
      .single();

    if (error) {
      console.error("Error updating filter view:", error);
      return sendApiError(c, "Failed to update filter view", 500);
    }

    return sendApiResponse(c, {
      message: "Filter view updated successfully",
      data: updatedView,
    });
  } catch (err) {
    console.error("Update filter view error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

/**
 * Get filter views accessible by the user
 */
const getFilterViews = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }
    const form_id = c.req.param("form_id");
    if (!form_id) {
      return sendApiError(c, "Form ID is required", 400);
    }
    // Get views where user is either creator or has access
    const { data: views, error } = await supabase
      .from("automate_form_response_filter_view")
      .select("*")
      .or(`created_by.eq.${user.user.id},access.cs.{${user.user.id}}`)
      .eq("form_id", form_id);

    if (error) {
      console.error("Error fetching filter views:", error);
      return sendApiError(c, "Failed to fetch filter views", 500);
    }

    return sendApiResponse(c, {
      message: "Filter views retrieved successfully",
      data: views,
    });
  } catch (err) {
    console.error("Get filter views error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

/**
 * Delete a filter view
 */
const deleteFilterView = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const viewId = c.req.param("id");
    if (!viewId) {
      return sendApiError(c, "View ID is required", 400);
    }

    // Check if view exists and user has permission
    const { data: existingView, error: fetchError } = await supabase
      .from("automate_form_response_filter_view")
      .select("created_by")
      .eq("id", viewId)
      .single();

    if (fetchError || !existingView) {
      return sendApiError(c, "Filter view not found", 404);
    }

    // Only creator can delete the view
    if (existingView.created_by !== user.user.id) {
      return sendApiError(c, "You don't have permission to delete this view", 403);
    }

    const { error } = await supabase
      .from("automate_form_response_filter_view")
      .delete()
      .eq("id", viewId);

    if (error) {
      console.error("Error deleting filter view:", error);
      return sendApiError(c, "Failed to delete filter view", 500);
    }

    return sendApiResponse(c, {
      message: "Filter view deleted successfully"
    });
  } catch (err) {
    console.error("Delete filter view error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

/**
 * Validate filter data structure
 */
const validateFilterData = (data: FilterViewData): boolean => {
  try {
    // Validate columns if present
    if (data.columns && !Array.isArray(data.columns)) {
      return false;
    }

    // Validate filters if present
    if (data.filters) {
      if (!Array.isArray(data.filters)) return false;
      
      for (const filter of data.filters) {
        if (!filter.field || !filter.operator) return false;
      }
    }

    // Validate sorting if present
    if (data.sorting) {
      if (!Array.isArray(data.sorting)) return false;
      
      for (const sort of data.sorting) {
        if (!sort.field || !['asc', 'desc'].includes(sort.direction)) {
          return false;
        }
      }
    }

    return true;
  } catch (err) {
    return false;
  }
};

export {
  createFilterView,
  updateFilterView,
  getFilterViews,
  deleteFilterView,
};
