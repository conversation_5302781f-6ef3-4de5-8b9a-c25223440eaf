
import { Hono } from "hono";
import { verifySupabaseAuth } from './../middleware/auth.middleware';
import {
  createFilterView,
  updateFilterView,
  getFilterViews,
  deleteFilterView,
} from "../controllers/responseFilterView.controllers";

const filter = new Hono();

filter.use("*", verifySupabaseAuth);

// Create a new filter view
filter.post("/", createFilterView);

// Update an existing filter view
filter.put("/:id", updateFilterView);

// Get all accessible filter views
filter.get("/:form_id", getFilterViews);

// Delete a filter view
filter.delete("/:id", deleteFilterView);

export {filter};
