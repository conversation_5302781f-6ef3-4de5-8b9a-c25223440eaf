
import { <PERSON><PERSON> } from "hono";
import { verifySupabaseAuth } from "../middleware/auth.middleware";
import { addWorkspace, getWorkspace, updateWorkspace, registerUserToWorkspace, getWorkspaceMembers, getWorkspaceReferralCount } from "../controllers/workspace.controllers";
import {
  getAutomateformMembers,
  addWorkspaceMemberToAutomateform,
  changeRole,
  removeMemberFromAutomateform
} from "../controllers/automateform_member.controllers";

const workspace = new Hono();

// Workspace routes
workspace.post('/create', verifySupabaseAuth, addWorkspace);
workspace.get('/', verifySupabaseAuth, getWorkspace);
workspace.put('/update/:id', verifySupabaseAuth, updateWorkspace);
workspace.post('/member/add', verifySupabaseAuth, registerUserToWorkspace);
workspace.get('/referral-details', verifySupabaseAuth, getWorkspaceReferralCount);

// Workspace members routes
workspace.get('/members/:workspace_id', verifySupaba<PERSON><PERSON><PERSON>, getWorkspaceMembers);

// Automateform members routes
workspace.get('/automateform/members/:workspace_id', verifySupabaseAuth, getAutomateformMembers);
workspace.post('/automateform/members/add', verifySupabaseAuth, addWorkspaceMemberToAutomateform);
workspace.post('/automateform/members/role', verifySupabaseAuth, changeRole);
workspace.delete('/automateform/members/:id', verifySupabaseAuth, removeMemberFromAutomateform);

export { workspace };
