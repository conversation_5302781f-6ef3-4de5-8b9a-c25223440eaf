interface UserAgentInfo {
  browser: string;
  device: string;
  userAgent: string;
}

export function parseUserAgent(userAgent: string): UserAgentInfo {
  let browser = 'unknown';
  let device = 'unknown';

  if (!userAgent) {
    return { browser, device, userAgent: 'unknown' };
  }

  // Detect browser
  if (userAgent.includes('Firefox/')) {
    browser = 'Firefox';
  } else if (userAgent.includes('Chrome/') && !userAgent.includes('Edg/')) {
    browser = 'Chrome';
  } else if (userAgent.includes('Safari/') && !userAgent.includes('Chrome/')) {
    browser = 'Safari';
  } else if (userAgent.includes('Edg/')) {
    browser = 'Edge';
  } else if (userAgent.includes('OPR/') || userAgent.includes('Opera/')) {
    browser = 'Opera';
  }

  // Detect device type
  if (userAgent.includes('Mobile')) {
    device = 'Mobile';
  } else if (userAgent.includes('Tablet')) {
    device = 'Tablet';
  } else {
    device = 'Desktop';
  }

  // Additional mobile detection
  if (
    userAgent.includes('iPhone') ||
    userAgent.includes('iPad') ||
    userAgent.includes('Android')
  ) {
    if (userAgent.includes('iPad')) {
      device = 'Tablet';
    } else if (userAgent.includes('iPhone') || userAgent.includes('Android')) {
      device = 'Mobile';
    }
  }

  return {
    browser,
    device,
    userAgent
  };
}
