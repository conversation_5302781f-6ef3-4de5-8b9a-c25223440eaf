import type { Context } from "hono";
import { supabase } from "../db";
import { sendApiError, sendApiResponse } from "../utils/Response";
import { UsageService } from "../services/usage.service";

// Add this new function to get detailed workspace usage report
export const getWorkspaceUsageReport = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const { workspace_id } = c.req.param();
    if (!workspace_id) {
      return sendApiError(c, "Workspace ID is required", 400);
    }

    // Get current usage data
    const { data: usageData, error: usageError } = await supabase
      .from("automate_form_workspace_usage")
      .select("*")
      .eq("workspace_id", workspace_id)
      .gte("billing_cycle_end", new Date().toISOString())
      .order("billing_cycle_end", { ascending: false })
      .limit(1)
      .single();

    if (usageError?.code === 'PGRST116') {
      // PGRST116 is the error code for no rows returned
      return sendApiResponse(c, {
        message: "No active plan found. Please purchase a plan to continue using our services.",
        status: "NO_PLAN",
        workspace_id
      }, 403);
    }

    if (usageError) {
      console.error("Error fetching workspace usage:", usageError);
      return sendApiError(c, "Failed to fetch usage data", 500);
    }

    // Get usage limits
    const { data: limitsData, error: limitsError } = await supabase
      .from("automate_form_workspace_usage_limits")
      .select("*")
      .eq("workspace_id", workspace_id)
      .single();

    if (limitsError) {
      console.error("Error fetching workspace limits:", limitsError);
      return sendApiError(c, "Failed to fetch usage limits", 500);
    }

    // Get subscription details
    const { data: subscriptionData, error: subscriptionError } = await supabase
      .from("automate_form_module_subscription")
      .select(
        `
        id,
        num_users,
        validity,
        status,
        remarks,
        updated_at,
        app (id, name, caption, icon),
        module (
          id, 
          name, 
          caption, 
          yearly_price, 
          monthly_price,
          features,
          workspace_limit
        )
      `
      )
      .eq("workspace_id", workspace_id)
      .eq("status", "active");

    if (subscriptionError) {
      console.error("Error fetching subscription data:", subscriptionError);
      return sendApiError(c, "Failed to fetch subscription data", 500);
    }

    // Calculate usage percentages
    const usagePercentages = {
      storage: (usageData.storage_used_mb / limitsData.storage_limit_mb) * 100,
      submissions:
        (usageData.submissions_count / limitsData.monthly_submissions_limit) *
        100,
      forms: usageData.create_form_count
        ? (usageData.create_form_count / limitsData.create_form_limit) * 100
        : 0,
      teamMembers: usageData.team_member_count
        ? (usageData.team_member_count / limitsData.team_member_limit) * 100
        : 0,
      aiCredits: usageData.ai_credits_used
        ? (usageData.ai_credits_used / limitsData.ai_credits_limit) * 100
        : 0,
    };

    // Calculate days remaining in billing cycle
    const daysRemaining = Math.max(
      0,
      Math.ceil(
        (new Date(usageData.billing_cycle_end).getTime() -
          new Date().getTime()) /
          (1000 * 60 * 60 * 24)
      )
    );

    // Format subscription data for display
    const formattedSubscriptions = subscriptionData.map((sub) => ({
      id: sub.id,
      app: sub.app,
      plan: sub.module.name,
      features: sub.module.features,
      users: sub.num_users,
      expiresAt: sub.validity,
      daysUntilExpiration: Math.max(
        0,
        Math.ceil(
          (new Date(sub.validity).getTime() - new Date().getTime()) /
            (1000 * 60 * 60 * 24)
        )
      ),
      status: sub.status,
      workspaceLimits: sub.module.workspace_limit,
    }));

    return sendApiResponse(c, {
      currentUsage: {
        storage: {
          used: usageData.storage_used_mb,
          limit: limitsData.storage_limit_mb,
          percentage: usagePercentages.storage.toFixed(1),
        },
        submissions: {
          used: usageData.submissions_count,
          limit: limitsData.monthly_submissions_limit,
          percentage: usagePercentages.submissions.toFixed(1),
        },
        forms: {
          used: usageData.create_form_count || 0,
          // limit: limitsData.create_form_limit,
          // percentage: usagePercentages.forms.toFixed(1),
        },
        teamMembers: {
          used: usageData.team_member_count || 0,
          // limit: limitsData.team_member_limit,
          percentage: usagePercentages.teamMembers.toFixed(1),
        },
        aiCredits: {
          used: usageData.ai_credits_used || 0,
          limit: limitsData.ai_credits_limit,
          percentage: usagePercentages.aiCredits.toFixed(1),
        },
      },
      billingCycle: {
        start: usageData.billing_cycle_start,
        end: usageData.billing_cycle_end,
        daysRemaining,
      },
      planType: limitsData.plan_type,
      subscriptions: formattedSubscriptions,
    });
  } catch (err) {
    console.error("Get Workspace Usage Report Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
