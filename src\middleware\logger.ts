import type { Context, Next } from 'hono';

// Create a custom logger middleware for Hono
export const morganLogger = () => {
    return async (c: Context, next: Next) => {
        const startTime = Date.now();
        
        // Get request details before processing
        const method = c.req.method;
        const url = new URL(c.req.url).pathname;
        const userAgent = c.req.header('user-agent') || '-';
        const realIP = c.req.header('x-real-ip') || 
                      c.req.header('x-forwarded-for') || 
                      c.env?.remoteAddr || 
                      'unknown';

        await next();

        // Calculate response time
        const responseTime = Date.now() - startTime;
        
        // Get response details
        const status = c.res.status;
        const contentLength = c.res.headers.get('content-length') || '-';
        
        // Format the log message
        const timestamp = new Date().toISOString();
        const logMessage = `[${timestamp}] "${method} ${url}" ${status} ${contentLength} - ${responseTime}ms (${realIP}) "${userAgent}"`;
        
        // Color the status code based on the response
        let coloredStatus = status.toString();
        if (status >= 500) {
            coloredStatus = `\x1b[31m${status}\x1b[0m`; // Red for server errors
        } else if (status >= 400) {
            coloredStatus = `\x1b[33m${status}\x1b[0m`; // Yellow for client errors
        } else if (status >= 300) {
            coloredStatus = `\x1b[36m${status}\x1b[0m`; // Cyan for redirects
        } else if (status >= 200) {
            coloredStatus = `\x1b[32m${status}\x1b[0m`; // Green for success
        }

        // Print the formatted log
        console.log(logMessage.replace(status.toString(), coloredStatus));
    };
};
