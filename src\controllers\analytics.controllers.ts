

import type { Context } from "hono";
import { supabase } from "../db";
import { sendApiError, sendApiResponse } from "../utils/Response";

const getFormAnalytics = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const formId = c.req.param("formId");
      const from = c.req.query("from");
    const to = c.req.query("to");
    if (!formId) {
      return sendApiError(c, "Form ID is required", 400);
    }

    // Get form views with browser and device info
    const { data: viewData, error: viewError } = await supabase
      .from("automate_form_views")
      .select("ip_address, user_agent, browser, device")
      .eq("form_id", formId)
      .gte("viewed_at", from)
      .lte("viewed_at", to);

    if (viewError) {
      console.error("Form Views Error:", viewError);
      return sendApiError(c, "Failed to get form views", 500);
    }

    // Count unique combinations of IP and user agent
    const uniqueViews = new Set(
      (viewData || []).map((row) => `${row.ip_address}-${row.user_agent}`)
    );
    const viewCount = uniqueViews.size;

    // Calculate browser statistics
    const browserStats = (viewData || []).reduce((acc: Record<string, number>, row) => {
      const browser = row.browser || 'unknown';
      acc[browser] = (acc[browser] || 0) + 1;
      return acc;
    }, {});

    // Calculate device statistics
    const deviceStats = (viewData || []).reduce((acc: Record<string, number>, row) => {
      const device = row.device || 'unknown';
      acc[device] = (acc[device] || 0) + 1;
      return acc;
    }, {});

    // Get form submissions count
    const { count: submissionCount, error: submissionError } = await supabase
      .from("automate_form_responses")
      .select("*", { count: "exact" })
      .eq("form_id", formId);

    if (submissionError) {
      console.error("Form Submissions Error:", submissionError);
      return sendApiError(c, "Failed to get form submissions", 500);
    }

    // Calculate conversion rate
    const conversionRate = viewCount ? ((submissionCount || 0) / viewCount) * 100 : 0;

    return sendApiResponse(
      c,
      {
        analytics: {
          uniqueViews: viewCount,
          submissions: submissionCount || 0,
          conversionRate: Math.round(conversionRate * 100) / 100,
          browserStats,
          deviceStats
        },
      },
      200
    );
  } catch (err) {
    console.error("Get Form Analytics Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

const getWorkspaceAnalytics = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const workspaceId = c.req.param("workspaceId");
    if (!workspaceId) {
      return sendApiError(c, "Workspace ID is required", 400);
    }

    // Get all forms in the workspace
    const { data: forms, error: formsError } = await supabase
      .from("automate_forms")
      .select("id")
      .eq("workspace_id", workspaceId);

    if (formsError) {
      console.error("Workspace Forms Error:", formsError);
      return sendApiError(c, "Failed to get workspace forms", 500);
    }

    if (!forms || forms.length === 0) {
      return sendApiResponse(
        c,
        {
          analytics: {
            totalForms: 0,
            totalViews: 0,
            totalSubmissions: 0,
            averageConversionRate: 0,
          },
        },
        200
      );
    }

    const formIds = forms.map((form) => form.id);

    // Get total views
    const { count: totalViews, error: viewsError } = await supabase
      .from("automate_form_views")
      .select("*", { count: "exact" })
      .in("form_id", formIds);

    if (viewsError) {
      console.error("Total Views Error:", viewsError);
      return sendApiError(c, "Failed to get total views", 500);
    }

    // Get total submissions
    const { count: totalSubmissions, error: submissionsError } = await supabase
      .from("automate_form_responses")
      .select("*", { count: "exact" })
      .in("form_id", formIds);

    if (submissionsError) {
      console.error("Total Submissions Error:", submissionsError);
      return sendApiError(c, "Failed to get total submissions", 500);
    }

    // Calculate average conversion rate
    const averageConversionRate = totalViews
      ? ((totalSubmissions || 0) / totalViews) * 100
      : 0;

    return sendApiResponse(
      c,
      {
        analytics: {
          totalForms: forms.length,
          totalViews: totalViews || 0,
          totalSubmissions: totalSubmissions || 0,
          averageConversionRate: Math.round(averageConversionRate * 100) / 100,
        },
      },
      200
    );
  } catch (err) {
    console.error("Get Workspace Analytics Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
const getDeviceWiseResponseCount = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const formId = c.req.param("formId");
     const from = c.req.query("from");
    const to = c.req.query("to");
    if (!formId) {
      return sendApiError(c, "Form ID is required", 400);
    }

    // Fetch all responses for the form with device info
    const { data, error } = await supabase
      .from("automate_form_responses")
      .select("device")
      .eq("form_id", formId)
      .gte("submitted_at", from)
      .lte("submitted_at", to);

    if (error) {
      console.error("Device Response Count Error:", error);
      return sendApiError(c, "Failed to get device response count", 500);
    }

    // Group by actual device value (no hardcoding)
    const responseCounts: Record<string, number> = {};
    (data || []).forEach((row) => {
      const device = row.device || "unknown";
      responseCounts[device] = (responseCounts[device] || 0) + 1;
    });

    return sendApiResponse(c, { responseCounts }, 200);
  } catch (err) {
    console.error("Get Device Wise Response Count Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
const getResponseTrend = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const formId = c.req.query("formId");
    const from = c.req.query("from");
    const to = c.req.query("to");
    if (!formId || !from || !to) {
      return sendApiError(c, "formId, from, and to are required", 400);
    }

    // Fetch all responses for the form in the date range
    const { data, error } = await supabase
      .from("automate_form_responses")
      .select("submitted_at")
      .eq("form_id", formId)
      .gte("submitted_at", from)
      .lte("submitted_at", to);

    if (error) {
      console.error("Response Trend Error:", error);
      return sendApiError(c, "Failed to get response trend", 500);
    }

    // Calculate range in days
    const startDate = new Date(from);
    const endDate = new Date(to);
    const msInDay = 1000 * 60 * 60 * 24;
    const daysDiff = Math.floor((endDate.getTime() - startDate.getTime()) / msInDay) + 1;

    // Prepare trends
    const dateTrend: Record<string, number> = {};
    const weekTrend: Record<string, number> = {};
    const monthTrend: Record<string, number> = {};
    const hourTrend: Record<string, number> = {};

    // Helper for ISO week string
    function getISOWeek(date: Date) {
      const temp = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
      const dayNum = temp.getUTCDay() || 7;
      temp.setUTCDate(temp.getUTCDate() + 4 - dayNum);
      const yearStart = new Date(Date.UTC(temp.getUTCFullYear(),0,1));
      const weekNum = Math.ceil((((temp.getTime() - yearStart.getTime()) / msInDay) + 1) / 7);
      return `${temp.getUTCFullYear()}-W${weekNum.toString().padStart(2,'0')}`;
    }

    (data || []).forEach((row) => {
      if (!row.submitted_at) return;
      const dt = new Date(row.submitted_at);
      const date = row.submitted_at.slice(0, 10);
      const month = `${dt.getFullYear()}-${(dt.getMonth()+1).toString().padStart(2,'0')}`;
      const week = getISOWeek(dt);
      dateTrend[date] = (dateTrend[date] || 0) + 1;
      weekTrend[week] = (weekTrend[week] || 0) + 1;
      monthTrend[month] = (monthTrend[month] || 0) + 1;
      if (from === to) {
        const hour = dt.getHours().toString().padStart(2, '0');
        hourTrend[hour] = (hourTrend[hour] || 0) + 1;
      }
    });

    let trend: { label: string, count: number }[] = [];
    if (from === to) {
      // Hourly for single day
      for (let h = 0; h < 24; h++) {
        const hour = h.toString().padStart(2, '0');
        trend.push({ label: hour, count: hourTrend[hour] || 0 });
      }
    } else if (daysDiff <= 31) {
      // Daily for up to 31 days
      for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
        const key = d.toISOString().slice(0, 10);
        trend.push({ label: key, count: dateTrend[key] || 0 });
      }
    } else if (daysDiff <= 90) {
      // Weekly for up to 90 days
      // Find all weeks in range
      const weekSet = new Set<string>();
      for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
        weekSet.add(getISOWeek(d));
      }
      Array.from(weekSet).sort().forEach((week) => {
        trend.push({ label: week, count: weekTrend[week] || 0 });
      });
    } else {
      // Monthly for longer ranges
      const monthSet = new Set<string>();
      for (let d = new Date(startDate); d <= endDate; d.setMonth(d.getMonth() + 1)) {
        const key = `${d.getFullYear()}-${(d.getMonth()+1).toString().padStart(2,'0')}`;
        monthSet.add(key);
      }
      Array.from(monthSet).sort().forEach((month) => {
        trend.push({ label: month, count: monthTrend[month] || 0 });
      });
    }

    return sendApiResponse(c, { trend }, 200);
  } catch (err) {
    console.error("Get Response Trend Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
const getViewTrend = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const formId = c.req.query("formId");
    const from = c.req.query("from");
    const to = c.req.query("to");
    if (!formId || !from || !to) {
      return sendApiError(c, "formId, from, and to are required", 400);
    }

    // Fetch all views for the form in the date range
    const { data, error } = await supabase
      .from("automate_form_views")
      .select("viewed_at")
      .eq("form_id", formId)
      .gte("viewed_at", from)
      .lte("viewed_at", to);

    if (error) {
      console.error("View Trend Error:", error);
      return sendApiError(c, "Failed to get view trend", 500);
    }

    // Calculate range in days
    const startDate = new Date(from);
    const endDate = new Date(to);
    const msInDay = 1000 * 60 * 60 * 24;
    const daysDiff = Math.floor((endDate.getTime() - startDate.getTime()) / msInDay) + 1;

    // Prepare trends
    const dateTrend: Record<string, number> = {};
    const weekTrend: Record<string, number> = {};
    const monthTrend: Record<string, number> = {};
    const hourTrend: Record<string, number> = {};

    // Helper for ISO week string
    function getISOWeek(date: Date) {
      const temp = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
      const dayNum = temp.getUTCDay() || 7;
      temp.setUTCDate(temp.getUTCDate() + 4 - dayNum);
      const yearStart = new Date(Date.UTC(temp.getUTCFullYear(),0,1));
      const weekNum = Math.ceil((((temp.getTime() - yearStart.getTime()) / msInDay) + 1) / 7);
      return `${temp.getUTCFullYear()}-W${weekNum.toString().padStart(2,'0')}`;
    }

    (data || []).forEach((row) => {
      if (!row.viewed_at) return;
      const dt = new Date(row.viewed_at);
      const date = row.viewed_at.slice(0, 10);
      const month = `${dt.getFullYear()}-${(dt.getMonth()+1).toString().padStart(2,'0')}`;
      const week = getISOWeek(dt);
      dateTrend[date] = (dateTrend[date] || 0) + 1;
      weekTrend[week] = (weekTrend[week] || 0) + 1;
      monthTrend[month] = (monthTrend[month] || 0) + 1;
      if (from === to) {
        const hour = dt.getHours().toString().padStart(2, '0');
        hourTrend[hour] = (hourTrend[hour] || 0) + 1;
      }
    });

    let trend: { label: string, count: number }[] = [];
    if (from === to) {
      // Hourly for single day
      for (let h = 0; h < 24; h++) {
        const hour = h.toString().padStart(2, '0');
        trend.push({ label: hour, count: hourTrend[hour] || 0 });
      }
    } else if (daysDiff <= 31) {
      // Daily for up to 31 days
      for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
        const key = d.toISOString().slice(0, 10);
        trend.push({ label: key, count: dateTrend[key] || 0 });
      }
    } else if (daysDiff <= 90) {
      // Weekly for up to 90 days
      const weekSet = new Set<string>();
      for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
        weekSet.add(getISOWeek(d));
      }
      Array.from(weekSet).sort().forEach((week) => {
        trend.push({ label: week, count: weekTrend[week] || 0 });
      });
    } else {
      // Monthly for longer ranges
      const monthSet = new Set<string>();
      for (let d = new Date(startDate); d <= endDate; d.setMonth(d.getMonth() + 1)) {
        const key = `${d.getFullYear()}-${(d.getMonth()+1).toString().padStart(2,'0')}`;
        monthSet.add(key);
      }
      Array.from(monthSet).sort().forEach((month) => {
        trend.push({ label: month, count: monthTrend[month] || 0 });
      });
    }

    return sendApiResponse(c, { trend }, 200);
  } catch (err) {
    console.error("Get View Trend Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

export { getFormAnalytics, getWorkspaceAnalytics, getDeviceWiseResponseCount, getResponseTrend, getViewTrend };
