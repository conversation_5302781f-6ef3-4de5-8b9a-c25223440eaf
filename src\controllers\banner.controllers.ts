import type { Context } from "hono";
import { supabase } from "../db";
import { sendApiError, sendApiResponse } from "../utils/Response";
import { upload } from "../utils/upload";
import { BUCKET_COMMON } from "../constant";

// Create a new banner
const createBanner = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) return sendApiError(c, "Unauthorized", 401);

    const formData = await c.req.formData();
    const name = formData.get("name")?.toString();

    if (!name) {
      return sendApiError(c, "Name is required", 400);
    }

    // Upload banner image if provided
    let bannerUrl = "";
    const bannerFile = formData.get("banner") as File | null;
    
    if (!bannerFile) {
      return sendApiError(c, "Banner image is required", 400);
    }

    if (bannerFile && bannerFile.name) {
      bannerUrl = await upload(bannerFile, BUCKET_COMMON);
    }

    const newBanner = {
      name,
      banner_url: bannerUrl,
      created_by: user.user.id,
    };

    const { data, error } = await supabase
      .from("banners")
      .insert([newBanner])
      .select("id, name, category, banner_url, created_at");

    if (error) {
      console.error("Create Banner Error:", error);
      return sendApiError(c, "Failed to create banner", 500);
    }

    return sendApiResponse(c, { banner: data[0] }, 201);
  } catch (err) {
    console.error("Create Banner Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

// Get all banners with optional category filter
const getBanners = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) return sendApiError(c, "Unauthorized", 401);

    const category = c.req.query("category");
    const page = parseInt(c.req.query("page") || "1");
    const limit = parseInt(c.req.query("limit") || "10");
    const offset = (page - 1) * limit;

    let query = supabase
      .from("banners")
      .select("id, name, category, banner_url, created_at", { count: "exact" })
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1);

    if (category) {
      query = query.eq("category", category);
    }

    const { data: banners, error, count } = await query;

    if (error) {
      console.error("Get Banners Error:", error);
      return sendApiError(c, "Failed to retrieve banners", 500);
    }

    const totalPages = Math.ceil((count || 0) / limit);

    return sendApiResponse(c, {
      banners: banners || [],
      pagination: {
        current_page: page,
        total_pages: totalPages,
        total_count: count || 0,
        limit
      }
    }, 200);
  } catch (err) {
    console.error("Get Banners Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

// Get banner by ID
const getBannerById = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) return sendApiError(c, "Unauthorized", 401);

    const bannerId = c.req.param("id");
    if (!bannerId) {
      return sendApiError(c, "Banner ID is required", 400);
    }

    const { data: banner, error } = await supabase
      .from("banners")
      .select("id, name, category, banner_url, created_at, created_by")
      .eq("id", bannerId)
      .single();

    if (error || !banner) {
      console.error("Get Banner Error:", error);
      return sendApiError(c, "Banner not found", 404);
    }

    return sendApiResponse(c, { banner }, 200);
  } catch (err) {
    console.error("Get Banner Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

// Update banner
const updateBanner = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) return sendApiError(c, "Unauthorized", 401);

    const bannerId = c.req.param("id");
    if (!bannerId) {
      return sendApiError(c, "Banner ID is required", 400);
    }

    // Check if banner exists and user has permission to update
    const { data: existingBanner, error: fetchError } = await supabase
      .from("banners")
      .select("id, created_by")
      .eq("id", bannerId)
      .single();

    if (fetchError || !existingBanner) {
      return sendApiError(c, "Banner not found", 404);
    }

    // Check if user is the creator or admin
    const { data: userProfile } = await supabase
      .from("user_profile")
      .select("role")
      .eq("id", user.user.id)
      .single();

    const isAdmin = userProfile?.role === "admin";
    const isOwner = existingBanner.created_by === user.user.id;

    if (!isAdmin && !isOwner) {
      return sendApiError(c, "You don't have permission to update this banner", 403);
    }

    const formData = await c.req.formData();
    const name = formData.get("name")?.toString();
    const category = formData.get("category")?.toString();
    const bannerFile = formData.get("banner") as File | null;

    const updatedBanner: any = {
      updated_at: new Date().toISOString(),
    };

    if (name) updatedBanner.name = name;
    if (category) updatedBanner.category = category;

    // Upload new banner image if provided
    if (bannerFile && bannerFile.name) {
      const bannerUrl = await upload(bannerFile, BUCKET_COMMON);
      updatedBanner.banner_url = bannerUrl;
    }

    const { data, error } = await supabase
      .from("banners")
      .update(updatedBanner)
      .eq("id", bannerId)
      .select("id, name, category, banner_url, created_at, updated_at, created_by");

    if (error) {
      console.error("Update Banner Error:", error);
      return sendApiError(c, "Failed to update banner", 500);
    }

    return sendApiResponse(c, { banner: data[0] }, 200);
  } catch (err) {
    console.error("Update Banner Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

// Delete banner
const deleteBanner = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) return sendApiError(c, "Unauthorized", 401);

    const bannerId = c.req.param("id");
    if (!bannerId) {
      return sendApiError(c, "Banner ID is required", 400);
    }

    // Check if banner exists and user has permission to delete
    const { data: existingBanner, error: fetchError } = await supabase
      .from("banners")
      .select("id, created_by")
      .eq("id", bannerId)
      .single();

    if (fetchError || !existingBanner) {
      return sendApiError(c, "Banner not found", 404);
    }

    // Check if user is the creator or admin
    const { data: userProfile } = await supabase
      .from("user_profile")
      .select("role")
      .eq("id", user.user.id)
      .single();

    const isAdmin = userProfile?.role === "admin";
    const isOwner = existingBanner.created_by === user.user.id;

    if (!isAdmin && !isOwner) {
      return sendApiError(c, "You don't have permission to delete this banner", 403);
    }

    const { error } = await supabase
      .from("banners")
      .delete()
      .eq("id", bannerId);

    if (error) {
      console.error("Delete Banner Error:", error);
      return sendApiError(c, "Failed to delete banner", 500);
    }

    return sendApiResponse(c, { message: "Banner deleted successfully" }, 200);
  } catch (err) {
    console.error("Delete Banner Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

// Get banners grouped by category
const getBannersGroupedByCategory = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) return sendApiError(c, "Unauthorized", 401);

    const { data: banners, error } = await supabase
      .from("banners")
      .select("id, name, category, banner_url, created_at, created_by")
      .order("category", { ascending: true })
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Get Banners Grouped Error:", error);
      return sendApiError(c, "Failed to retrieve banners", 500);
    }

    // Group banners by category
    const groupedBanners = (banners || []).reduce((acc: any, banner: any) => {
      const category = banner.category;
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(banner);
      return acc;
    }, {});

    // Convert to array format
    const bannersGrouped = Object.keys(groupedBanners).map(category => ({
      category,
      banners: groupedBanners[category]
    }));

    return sendApiResponse(c, { banners_by_category: bannersGrouped }, 200);
  } catch (err) {
    console.error("Get Banners Grouped Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

// Get unique categories
const getBannerCategories = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) return sendApiError(c, "Unauthorized", 401);

    const { data: categories, error } = await supabase
      .from("banners")
      .select("category")
      .order("category", { ascending: true });

    if (error) {
      console.error("Get Banner Categories Error:", error);
      return sendApiError(c, "Failed to retrieve categories", 500);
    }

    // Get unique categories
    const uniqueCategories = [...new Set((categories || []).map(item => item.category))];

    return sendApiResponse(c, { categories: uniqueCategories }, 200);
  } catch (err) {
    console.error("Get Banner Categories Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

export {
  createBanner,
  getBanners,
  getBannerById,
  updateBanner,
  deleteBanner,
  getBannersGroupedByCategory,
  getBannerCategories
};
