import type { Context } from "hono";
import { supabase } from "../db";
import { sendApiError, sendApiResponse } from "../utils/Response";
import { upload } from "../utils/upload";
import { BUCKET_COMMON } from "../constant";

// Create a new banner
const createBanner = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) return sendApiError(c, "Unauthorized", 401);

    const formData = await c.req.formData();
    const title = formData.get("title")?.toString();
    const description = formData.get("description")?.toString();
    if(!title || !description) {
      return sendApiError(c, "Title or description is required", 400);
    }

    // Upload banner image if provided
    let bannerUrl = "";
    const bannerFile = formData.get("banner") as File | null;
    
    if (!bannerFile) {
      return sendApiError(c, "Banner image is required", 400);
    }

    if (bannerFile && bannerFile.name) {
      bannerUrl = await upload(bannerFile, BUCKET_COMMON);
    }

    const newBanner = {
      title: title || null,
      description: description || null,
      banner_url: bannerUrl
    };
    const { data: userProfile } = await supabase
      .from("user_profile")
      .select("role")
      .eq("id", user.user.id)
      .single();

    const isAdmin = userProfile?.role === "admin";

    if (!isAdmin) {
      return sendApiError(c, "You don't have permission to update this banner", 403);
    }

    const { data, error } = await supabase
      .from("automate_form_banners")
      .insert([newBanner])
      .select("id, title, description, banner_url, created_at");

    if (error) {
      console.error("Create Banner Error:", error);
      return sendApiError(c, "Failed to create banner", 500);
    }

    return sendApiResponse(c, { banner: data[0] }, 201);
  } catch (err) {
    console.error("Create Banner Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

// Get all banners with pagination
const getBanners = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) return sendApiError(c, "Unauthorized", 401);

    const page = parseInt(c.req.query("page") || "1");
    const limit = parseInt(c.req.query("limit") || "10");
    const offset = (page - 1) * limit;

    const query = supabase
      .from("automate_form_banners")
      .select("id, title, description, banner_url, created_at", { count: "exact" })
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1);

    const { data: banners, error, count } = await query;

    if (error) {
      console.error("Get Banners Error:", error);
      return sendApiError(c, "Failed to retrieve banners", 500);
    }

    const totalPages = Math.ceil((count || 0) / limit);

    return sendApiResponse(c, {
      banners: banners || [],
      pagination: {
        current_page: page,
        total_pages: totalPages,
        total_count: count || 0,
        limit
      }
    }, 200);
  } catch (err) {
    console.error("Get Banners Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

// Get banner by ID
const getBannerById = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) return sendApiError(c, "Unauthorized", 401);

    const bannerId = c.req.param("id");
    if (!bannerId) {
      return sendApiError(c, "Banner ID is required", 400);
    }

    const { data: banner, error } = await supabase
      .from("automate_form_banners")
      .select("id,title, description, banner_url, created_at")
      .eq("id", bannerId)
      .single();

    if (error || !banner) {
      console.error("Get Banner Error:", error);
      return sendApiError(c, "Banner not found", 404);
    }

    return sendApiResponse(c, { banner }, 200);
  } catch (err) {
    console.error("Get Banner Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

// Update banner
const updateBanner = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) return sendApiError(c, "Unauthorized", 401);

    const bannerId = c.req.param("id");
    if (!bannerId) {
      return sendApiError(c, "Banner ID is required", 400);
    }

    // Check if banner exists and user has permission to update
    const { data: existingBanner, error: fetchError } = await supabase
      .from("automate_form_banners")
      .select("id")
      .eq("id", bannerId)
      .single();

    if (fetchError || !existingBanner) {
      return sendApiError(c, "Banner not found", 404);
    }

    // Check if user is the creator or admin
    const { data: userProfile } = await supabase
      .from("user_profile")
      .select("role")
      .eq("id", user.user.id)
      .single();

    const isAdmin = userProfile?.role === "admin";

    if (!isAdmin) {
      return sendApiError(c, "You don't have permission to update this banner", 403);
    }

    const formData = await c.req.formData();
    const title = formData.get("title")?.toString();
    const description = formData.get("description")?.toString();
    const bannerFile = formData.get("banner") as File | null;

    const updatedBanner: any = {
      updated_at: new Date().toISOString(),
    };

    if (title) updatedBanner.title = title;
    if (description) updatedBanner.description = description;

    // Upload new banner image if provided
    if (bannerFile && bannerFile.name) {
      const bannerUrl = await upload(bannerFile, BUCKET_COMMON);
      updatedBanner.banner_url = bannerUrl;
    }

    const { data, error } = await supabase
      .from("automate_form_banners")
      .update(updatedBanner)
      .eq("id", bannerId)
      .select("id, title, description, banner_url, created_at, updated_at");

    if (error) {
      console.error("Update Banner Error:", error);
      return sendApiError(c, "Failed to update banner", 500);
    }

    return sendApiResponse(c, { banner: data[0] }, 200);
  } catch (err) {
    console.error("Update Banner Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

// Delete banner
const deleteBanner = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) return sendApiError(c, "Unauthorized", 401);

    const bannerId = c.req.param("id");
    if (!bannerId) {
      return sendApiError(c, "Banner ID is required", 400);
    }

    // Check if banner exists and user has permission to delete
    const { data: existingBanner, error: fetchError } = await supabase
      .from("automate_form_banners")
      .select("id")
      .eq("id", bannerId)
      .single();

    if (fetchError || !existingBanner) {
      return sendApiError(c, "Banner not found", 404);
    }

    // Check if user is the creator or admin
    const { data: userProfile } = await supabase
      .from("user_profile")
      .select("role")
      .eq("id", user.user.id)
      .single();

    const isAdmin = userProfile?.role === "admin";

    if (!isAdmin) {
      return sendApiError(c, "You don't have permission to delete this banner", 403);
    }

    const { error } = await supabase
      .from("automate_form_banners")
      .delete()
      .eq("id", bannerId);

    if (error) {
      console.error("Delete Banner Error:", error);
      return sendApiError(c, "Failed to delete banner", 500);
    }

    return sendApiResponse(c, { message: "Banner deleted successfully" }, 200);
  } catch (err) {
    console.error("Delete Banner Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};



export {
  createBanner,
  getBanners,
  getBannerById,
  updateBanner,
  deleteBanner
};
