import dotenv from "dotenv";
dotenv.config({ path: "./.env" });

import { supabase } from "../db";
import axios from "axios";

const OUTLOOK_CLIENT_ID = process.env.OUTLOOK_CLIENT_ID;
const OUTLOOK_CLIENT_SECRET = process.env.OUTLOOK_CLIENT_SECRET;
const OUTLOOK_REDIRECT_URI = process.env.OUTLOOK_REDIRECT_URI;

interface OutlookProfile {
  id: string;
  displayName: string;
  mail: string;
  userPrincipalName: string;
}

interface EmailAttachment {
  name: string;
  contentType: string;
  contentBytes: string;
}

/**
 * Get valid Outlook token (refresh if needed)
 */
export const getValidOutlookToken = async (credentialId: number): Promise<string | null> => {
  try {
    const { data: credential, error } = await supabase
      .from('automate_form_integration_credentials')
      .select('auth_data')
      .eq('id', credentialId)
      .single();

    if (error || !credential) {
      console.error('Failed to get credential:', error);
      return null;
    }

    const { access_token, refresh_token, expires_at } = credential.auth_data;

    if (!access_token) {
      return null;
    }

    // Check if token is expired
    const now = new Date();
    const expiresAt = new Date(expires_at);

    if (now >= expiresAt && refresh_token) {
      // Token is expired, refresh it
      const newTokens = await refreshOutlookToken(refresh_token);
      
      if (newTokens) {
        // Update the credential with new tokens
        await supabase
          .from('automate_form_integration_credentials')
          .update({
            auth_data: {
              ...credential.auth_data,
              access_token: newTokens.access_token,
              refresh_token: newTokens.refresh_token || refresh_token,
              expires_at: new Date(Date.now() + newTokens.expires_in * 1000).toISOString()
            }
          })
          .eq('id', credentialId);

        return newTokens.access_token;
      }
    }

    return access_token;
  } catch (error) {
    console.error('Error getting valid Outlook token:', error);
    return null;
  }
};

/**
 * Refresh Outlook access token
 */
const refreshOutlookToken = async (refreshToken: string) => {
  try {
    const response = await axios.post('https://login.microsoftonline.com/common/oauth2/v2.0/token', 
      new URLSearchParams({
        client_id: OUTLOOK_CLIENT_ID || '',
        client_secret: OUTLOOK_CLIENT_SECRET || '',
        refresh_token: refreshToken,
        grant_type: 'refresh_token'
      }),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );

    return response.data;
  } catch (error) {
    console.error('Error refreshing Outlook token:', error);
    return null;
  }
};

/**
 * Get Outlook user profile
 */
export const getOutlookProfile = async (credentialId: number): Promise<OutlookProfile | null> => {
  try {
    const token = await getValidOutlookToken(credentialId);
    
    if (!token) {
      return null;
    }

    const response = await axios.get('https://graph.microsoft.com/v1.0/me', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    return response.data;
  } catch (error) {
    console.error('Error getting Outlook profile:', error);
    return null;
  }
};

/**
 * Send email via Microsoft Graph API
 */
export const sendOutlookMessage = async (
  token: string,
  to: string,
  from: string,
  subject: string,
  body: string,
  isHtml: boolean = false,
  cc?: string,
  bcc?: string,
  attachments?: EmailAttachment[]
): Promise<boolean> => {
  try {
    const message = {
      message: {
        subject: subject,
        body: {
          contentType: isHtml ? 'HTML' : 'Text',
          content: body
        },
        toRecipients: [
          {
            emailAddress: {
              address: to
            }
          }
        ],
        from: {
          emailAddress: {
            address: from
          }
        },
        attachments: attachments?.map(att => ({
          '@odata.type': '#microsoft.graph.fileAttachment',
          name: att.name,
          contentType: att.contentType,
          contentBytes: att.contentBytes
        })) || []
      }
    };

    const response = await axios.post(
      'https://graph.microsoft.com/v1.0/me/sendMail',
      message,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );

    console.log(`✅ Outlook email sent to ${to}`);
    return response.status === 202;
  } catch (error: any) {
    console.error('Error sending Outlook email:', error.response?.data || error.message);
    return false;
  }
};

/**
 * Process email template with form data
 */
export const processEmailTemplate = (
  template: string,
  formData: any[],
  formTitle: string = 'Form Submission'
): { subject: string; body: string } => {
  let processedTemplate = template;

  // Replace form title
  processedTemplate = processedTemplate.replace(/\{\{form_title\}\}/g, formTitle);

  // Replace form fields
  formData.forEach((field: any) => {
    const fieldName = field.name;
    const fieldValue = typeof field.value === 'object' && field.value !== null
      ? Object.values(field.value).join(' ')
      : field.value || '';

    // Replace field placeholders
    const regex = new RegExp(`\\{\\{${fieldName}\\}\\}`, 'g');
    processedTemplate = processedTemplate.replace(regex, fieldValue);
  });

  // Extract subject and body
  const subjectMatch = processedTemplate.match(/Subject:\s*(.+?)(?:\n|$)/);
  const subject = subjectMatch ? subjectMatch[1].trim() : 'Form Submission Confirmation';

  // Remove subject line from body
  const body = processedTemplate.replace(/Subject:\s*.+?(?:\n|$)/, '').trim();

  return { subject, body };
};

/**
 * Create default email template
 */
export const createDefaultEmailTemplate = (customMessage?: string): string => {
  return `Subject: Thank you for your submission!

<html>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
  <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
    <h2 style="color: #2c3e50;">Thank you for your submission!</h2>
    
    <p>${customMessage || 'We have received your form submission and will get back to you soon.'}</p>
    
    <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
      <h3 style="margin-top: 0; color: #495057;">Your Submission Details:</h3>
      <p><strong>Form:</strong> {{form_title}}</p>
      <p><strong>Submitted at:</strong> ${new Date().toLocaleString()}</p>
    </div>
    
    <p>If you have any questions, please don't hesitate to contact us.</p>
    
    <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6;">
      <p style="color: #6c757d; font-size: 14px;">
        This email was sent automatically from AutomateForm.ai
      </p>
    </div>
  </div>
</body>
</html>`;
};

/**
 * Extract email from form data
 */
export const extractEmailFromFormData = (formData: any[]): string | null => {
  // Common email field names
  const emailFields = ['email', 'Email', 'EMAIL', 'email_address', 'emailAddress', 'user_email'];
  
  for (const field of formData) {
    if (emailFields.includes(field.name) && field.value) {
      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (emailRegex.test(field.value)) {
        return field.value;
      }
    }
  }
  
  return null;
};

/**
 * Exchange authorization code for tokens
 */
export const exchangeCodeForTokens = async (code: string) => {
  try {
    const response = await axios.post(
      'https://login.microsoftonline.com/common/oauth2/v2.0/token',
      new URLSearchParams({
        client_id: OUTLOOK_CLIENT_ID || '',
        client_secret: OUTLOOK_CLIENT_SECRET || '',
        code: code,
        redirect_uri: OUTLOOK_REDIRECT_URI || '',
        grant_type: 'authorization_code'
      }),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );

    return response.data;
  } catch (error: any) {
    console.error('Error exchanging code for tokens:', error.response?.data || error.message);
    return null;
  }
};
