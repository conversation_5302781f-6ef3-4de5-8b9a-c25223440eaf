import type { Context } from "hono";
import { supabase } from "../db";
import { sendApiError, sendApiResponse } from "../utils/Response";
import { v4 as uuidv4 } from "uuid";
import { UsageService } from "../services/usage.service";
import { parseUserAgent } from '../utils/userAgentParser';
import { canUserEditForm, canUserViewForm, canUserDeleteForm } from '../utils/FormPermissionUtils';
const createForm = async (c: Context) => {
  try {
    const user = c.get("user");

    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }
    const { data: userdata, error: userDataerror } = await supabase
      .from("user_profile")
      .select("workspace_id")
      .eq("id", user.user.id)
      .single();
    if (userDataerror || !userdata) {
      return sendApiError(c, "Failed to retrieve user profile", 500);
    }
    const body = await c.req.json();
    console.log("📢 Received form data:", body);

    if (!body.type) {
      return sendApiError(c, "Form type is required", 400);
    }

    // Check if form creation limit has been reached
    // if (userdata.workspace_id) {
    //   const usageData = await UsageService.getWorkspaceUsage(
    //     userdata.workspace_id
    //   );
    //   if (usageData) {
    //     // Check if this form creation would exceed the limit
    //     if (
    //       usageData.usage.create_form_count >=
    //       usageData.limits.create_form_limit
    //     ) {
    //       return sendApiError(
    //         c,
    //         "Form creation limit reached. Please upgrade your plan to create more forms.",
    //         403
    //       );
    //     }
    //   }
    // }

    const newForm:any = {
      type: body.type,
      created_by: user.user.id,
      workspace_id: userdata.workspace_id
    };
  if (body.folder_id) {
      newForm.folder_id = body.folder_id;
    }
    const { data, error } = await supabase
      .from("automate_forms")
      .insert([newForm])
      .select("id");

    if (error || !data || data.length === 0) {
      console.error("Create Form Error:", error);
      return sendApiError(c, "Failed to create form", 500);
    }
    const formId = data[0].id;

    // Track form creation in workspace usage
    if (userdata.workspace_id) {
      await UsageService.incrementFormCreationCount(userdata.workspace_id);
    }

    // Default settings for the form
    const formSettings = {
      form_id: formId,
      is_public: true,
      accept_responses: true,
    };

    // Insert form settings
    const { error: settingsError } = await supabase
      .from("automate_form_settings")
      .insert([formSettings]);

    if (settingsError) {
      console.error("Create Form Settings Error:", settingsError);
      return sendApiError(c, "Form created, but failed to save settings", 500);
    }

    return sendApiResponse(c, { form_id: data[0].id }, 201);
  } catch (err) {
    console.error("Create Form Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
const updateForm = async (c: Context) => {
  try {
    const user = c.get("user");

    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const formId = c.req.param("id");
    if (!formId) {
      return sendApiError(c, "Form ID is required", 400);
    }

    const body = await c.req.json();
    console.log("Update Form Data:", body);

    if (
      !body.title &&
      !body.description &&
      !body.type &&
      !body.formheading &&
      !body.header_img===undefined &&
      body.bg_image === undefined &&
      body.bg_color === undefined &&
      body.heading_color === undefined &&
      body.description_color === undefined &&
      !body.font_family &&
      !body.button_properties
    ) {
      return sendApiError(c, "At least one field is requiredfor update", 400);
    }

    // Check if user can edit this form
    const canEdit = await canUserEditForm(user.user.id, formId);
    if (!canEdit) {
      return sendApiError(c, "You don't have permission to edit this form", 403);
    }

    const updatedFields: Record<string, any> = {
      ...(body.title && { title: body.title }),
      ...(body.description && { description: body.description }),
      ...(body.type && { type: body.type }),
      ...(body.formheading && { heading: body.formheading }),
      ...(body.header_img !== undefined ? { header_img: body.header_img } : {}),
      ...(body.bg_image !== undefined ? { bg_image: body.bg_image } : {}),
      ...(body.bg_color !== undefined ? { bg_color: body.bg_color } : {}),
      ...(body.heading_color !== undefined
        ? { heading_color: body.heading_color }
        : {}),
      ...(body.description_color !== undefined
        ? { description_color: body.description_color }
        : {}),
      ...(body.font_family && { font_family: body.font_family }),
      ...(body.button_properties && {
        button_properties: body.button_properties,
      }),
      updated_at: new Date().toISOString(),
    };

    // Update form (no need to filter by created_by since we already checked permissions)
    const { data, error } = await supabase
      .from("automate_forms")
      .update(updatedFields)
      .eq("id", formId)
      .select();

    if (error || !data || data.length === 0) {
      console.error("Update Form Error:", error);
      return sendApiError(c, "Failed to update form", 500);
    }
    console.log("Updated Form:", data[0]);
    return sendApiResponse(c, { updateForm: data[0] }, 200);
  } catch (err) {
    console.error("Update Form Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
const getUserForms = async (c: Context) => {
  try {
    const user = c.get("user");

    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }
    const { data: userdata, error: userDataerror } = await supabase
      .from("user_profile")
      .select("workspace_id")
      .eq("id", user.user.id)
      .single();
    if (userDataerror || !userdata) {
      return sendApiError(c, "Failed to retrieve user profile", 500);
    }
    const limit = parseInt(c.req.query("limit") || "10", 10); // Default limit to 10
    const offset = parseInt(c.req.query("offset") || "0", 10);
    const p_created_by = c.req.query("p_created_by");

    const { count,data: forms, error: countError } = await supabase
      .from("automate_forms")
      .select("*", { count: "exact" })
      .eq("created_by", user.user.id)
      .eq("is_deleted", false);

    if (countError) {
      console.error("Fetch User Forms Count Error:", countError);
      return sendApiError(c, "Failed to fetch forms count", 500);
    }
    console.log("count", count);

    // Handle empty p_created_by parameter - convert empty string to null for UUID type
    const createdByParam = p_created_by && p_created_by.trim() !== "" ? p_created_by : null;

    const { data, error } = await supabase.rpc("get_user_forms_v4", {
      p_user_id: user.user.id,
      p_limit: limit,
      p_offset: offset,
      p_workspace_id: userdata.workspace_id,
      p_created_by: createdByParam
    });

    if (error) {
      console.error("Fetch User Forms Error:", error);
      return sendApiError(c, "Failed to fetch forms", 500);
    }
    if (!data || data.length === 0) {
      return sendApiResponse(c, { forms: [], total_count: count || 0 }, 200);
    }
    const formIds = data.map((form: any) => form.id);
    const { data: formFields, error: fieldsError } = await supabase
      .from("automate_form_fields")
      .select("form_id")
      .in("form_id", formIds);
    if (fieldsError) {
      console.error("❌ Fetch Form Fields Error:", fieldsError);
      return sendApiError(c, "Failed to check form fields", 500);
    }

    console.log("📢 Form Fields Data:", formFields);

    // Create a lookup to check if a form has fields
    const formFieldMap = new Set(formFields.map((field) => field.form_id));

    // Append isFieldCreated flag to each form
    const updatedForms = data.map((form: any) => ({
      ...form,
      isFieldCreated: formFieldMap.has(form.id),
    }));

    return sendApiResponse(
      c,
      { forms: updatedForms, total_count: count || 0 },
      200
    );
  } catch (err) {
    console.error("Unexpected Error in Fetching Forms:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
const publishForm = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const formId = c.req.param("id");
    if (!formId) {
      return sendApiError(c, "Form ID is required", 400);
    }
    // Check if user can edit this form (edit permission allows publishing)
    const canEdit = await canUserEditForm(user.user.id, formId);
    if (!canEdit) {
      return sendApiError(c, "You are not allowed to publish this form", 403);
    }
    const { data: formFields, error: fieldsError } = await supabase
      .from("automate_form_fields")
      .select("fields")
      .eq("form_id", formId)
      .single();

    if (fieldsError || !formFields || formFields.fields.length === 0) {
      return sendApiError(c, "Cannot publish form without fields", 400);
    }

    const { data: updatedForm, error: updateError } = await supabase
      .from("automate_forms")
      .update({
        published: true,
        updated_at: new Date().toISOString(),
      })
      .eq("id", formId)
      .eq("is_deleted", false)
      .select();

    if (updateError) {
      console.error("Publish Form Error:", updateError);
      return sendApiError(c, "Failed to publish form", 500);
    }

    if (!updatedForm || updatedForm.length === 0) {
      return sendApiError(c, "No changes were made. Form not updated.", 400);
    }

    console.log("✅ Published Form:", updatedForm[0]);
    return sendApiResponse(c, { form: updatedForm[0] }, 200);
  } catch (err) {
    console.error("Publish Form Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
const searchForms = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }
    const limit = parseInt(c.req.query("limit") || "10", 10); // Default limit to 10
    const offset = parseInt(c.req.query("offset") || "0", 10);
    const searchQuery = c.req.query("q");
    if (!searchQuery) {
      return sendApiError(c, "Search query is required", 400);
    }

    const { data, error } = await supabase.rpc("search_user_forms_v3", {
      p_user_id: user.user.id,
      search_query: searchQuery,
      p_limit: limit,
      p_offset: offset,
    });
    console.log("data", data);
    if (error) {
      console.error("Search Forms Error:", error);
      return sendApiError(c, "Failed to search forms", 500);
    }

    return sendApiResponse(
      c,
      { forms: data, serach_count: data.length || 0 },
      200
    );
  } catch (err) {
    console.error("Search Forms Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
const getPublicForm = async (c: Context) => {
  try {
    const formId = c.req.param("id");
    if (!formId) {
      return sendApiError(c, "Form ID is missing", 400);
    }    

    // Get all potential IP-related headers for debugging
    const allHeaders = {
      'x-forwarded-for': c.req.header('x-forwarded-for'),
      'x-real-ip': c.req.header('x-real-ip'),
      'cf-connecting-ip': c.req.header('cf-connecting-ip'),
      'user-agent': c.req.header('user-agent'),
      'host': c.req.header('host'),
      'origin': c.req.header('origin')
    };
    console.log("All request headers:", allHeaders);

    // Get IP address with enhanced fallback handling
    let ip_address = 'unknown';
    const forwarded = c.req.header('x-forwarded-for');
    const realIp = c.req.header('x-real-ip');
    const cfIp = c.req.header('cf-connecting-ip');
    // Safely get remote address if available
    let remoteAddr: string | undefined = undefined;
    const raw = c.req.raw as { socket?: { remoteAddress?: string } };
    if (raw.socket && typeof raw.socket.remoteAddress === 'string') {
      remoteAddr = raw.socket.remoteAddress;
    }

    // Try different methods to get IP
    if (forwarded) {
      ip_address = forwarded.split(',')[0].trim();
    } else if (realIp) {
      ip_address = realIp;
    } else if (cfIp) {
      ip_address = cfIp;
    } else if (remoteAddr) {
      // Clean up IPv6 format if present
      ip_address = remoteAddr.replace(/^::ffff:/, '').split(':')[0];
    }

    // Clean up the IP address
    if (ip_address !== 'unknown') {
      ip_address = ip_address.replace(/^::ffff:/, '');
      ip_address = ip_address.split(':')[0];
    }

    console.log("Detected IP address:", ip_address);

    // Parse user agent string and browser/device info
    const userAgentString = c.req.header('user-agent') || '';
    const { browser, device } = parseUserAgent(userAgentString);
    console.log("details",browser,device)

    // Store view with detected IP and browser info
     const { error: viewError } = await supabase
      .from("automate_form_views")
      .upsert([{ 
        form_id: formId, 
        viewed_at: new Date().toISOString(),
        ip_address: ip_address,
        user_agent: userAgentString, // Store full user agent string
        browser: browser,
        device: device
      }], {
        onConflict: 'form_id,ip_address',
        ignoreDuplicates: true,
      });

    if (viewError) {
      console.error("Form View Tracking Error:", viewError);
      // Continue execution even if view tracking fails
    }
    const { data: form, error: formError } = await supabase
      .from("automate_forms")
      .select(
        "id, title, description, type, heading, header_img, published, workspace_id, automate_form_settings(is_public,accept_responses,copy_allowed),bg_image,bg_color,heading_color,description_color,font_family,button_properties,is_deleted"
      )
      .eq("id", formId)
      .eq("published", true)
      .eq("is_deleted", false)
      .single();

    console.log("form", form);
    if (formError || !form) {
      return sendApiError(
        c,
        "Form not found or not available for submission",
        403
      );
    }
        const { data: formFields, error: fieldsError } = await supabase
      .from("automate_form_fields")
      .select("fields,condition")
      .eq("form_id", formId)
      .single();

    if (fieldsError || !formFields) {
      return sendApiError(c, "Form fields not found", 404);
    }
    const { data: formsetting, error: settingError } = await supabase
      .from("automate_form_settings")
      .select("thank_you_type,thank_you_data,thank_you_url,accept_responses")
      .eq("form_id", formId)
      .single();

    if (settingError || !formsetting) {
      return sendApiError(c, "settings  not found", 404);
    }
    const {data:reffreldate, error:refrelerror} = await supabase
      .from("referral_partners")
      .select("referral_code")
      .eq("workspace_id", form.workspace_id)
      .single();

    if (refrelerror || !reffreldate) {
      return sendApiError(c, "refrel not found", 404);
    }

    return sendApiResponse(
      c,
      {
        form,
        fields: formFields.fields || [],
        condition: formFields.condition || null,
        thank_you_type: formsetting.thank_you_type || null,
        thank_you_data: formsetting.thank_you_data || null,
        thank_you_url: formsetting.thank_you_url || null,
        accept_responses: formsetting.accept_responses || null,
        referral_code: reffreldate.referral_code || null,
      },
      200
    );
  } catch (err) {
    console.error("Error fetching public form:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
const getForm = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }
    const formId = c.req.param("id");
    if (!formId) {
      return sendApiError(c, "Form ID is missing", 400);
    }

    const { data: form, error: formError } = await supabase
      .from("automate_forms")
      .select(
        "id, title, description, type, heading, header_img, published,automate_form_settings(is_public,accept_responses,copy_allowed),bg_image,bg_color,heading_color,description_color,font_family,button_properties,workspace_id,is_deleted"
      )
      .eq("id", formId)
      .eq("is_deleted", false)
      .single();

    if (formError || !form) {
      return sendApiError(c, "Form not found", 403);
    }
    const { data: fieldData, error: fieldError } = await supabase
      .from("automate_form_fields")
      .select("id")
      .eq("form_id", formId)
      .limit(1);

    if (fieldError) {
      console.error("Error fetching form fields:", fieldError);
      return sendApiError(c, "Failed to check form fields", 500);
    }

    // Determine if the form has fields
    const isFieldCreated = fieldData && fieldData.length > 0;
    return sendApiResponse(
      c,
      {
        form: {
          ...form,
          isFieldCreated,
        },
      },
      200
    );
  } catch (err) {
    console.error("Error fetching public form:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
const cloneForm = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    } 
    const formId = c.req.param("id");
    if (!formId) {
      return sendApiError(c, "Form ID is required", 400);
    }

    // Fetch the original form
    const { data: form, error: formError } = await supabase
      .from("automate_forms")
      .select(
        "type, title, description, heading, header_img, created_by,workspace_id,folder_id,automate_form_settings(is_public,accept_responses)"
      )
      .eq("id", formId)
      .eq("is_deleted", false)
      .single();

    if (formError || !form) {
      return sendApiError(c, "Form not found", 404);
    }

    // if (form.workspace_id) {
    //   const usageData = await UsageService.getWorkspaceUsage(
    //     form.workspace_id
    //   );
    //   if (usageData) {
    //     // Check if this form creation would exceed the limit
    //     if (
    //       usageData.usage.create_form_count >=
    //       usageData.limits.create_form_limit
    //     ) {
    //       return sendApiError(
    //         c,
    //         "Form creation limit reached. Please upgrade your plan to create more forms.",
    //         403
    //       );
    //     }
    //   }
    // }
    // Clone the form
    const newForm = {
      type: form.type,
      title: form.title ? `${form.title} (Copy)` : null,
      description: form.description,
      heading: form.heading,
      header_img: form.header_img,
      created_by: user.user.id,
      workspace_id: form.workspace_id,
      folder_id:form.folder_id || null
    };

    const { data: newFormData, error: newFormError } = await supabase
      .from("automate_forms")
      .insert([newForm])
      .select("id")
      .single();

    if (newFormError || !newFormData) {
      return sendApiError(c, "Failed to clone form", 500);
    }
    

    const newFormId = newFormData.id;

    const formSettings = {
      form_id: newFormId,
      is_public: form?.automate_form_settings?.[0]?.is_public ?? false,
      accept_responses: form?.automate_form_settings?.[0]?.accept_responses ?? true,
    };
 if (form.workspace_id) {
      await UsageService.incrementFormCreationCount(form.workspace_id);
    }
    // Insert form settings
    const { error: settingsError } = await supabase
      .from("automate_form_settings")
      .insert([formSettings]);

    if (settingsError) {
      console.error("Create Form Settings Error:", settingsError);
      return sendApiError(c, "Form created, but failed to save settings", 500);
    }

    // Fetch the fields of the original form
    const { data: formFields, error: fieldsError } = await supabase
      .from("automate_form_fields")
      .select("fields,condition")
      .eq("form_id", formId)
      .single();

    if (fieldsError) {
      console.error("Error fetching form fields:", fieldsError);
      return sendApiError(c, "Failed to fetch form fields", 500);
    }

    if (formFields) {
      // Clone the fields
      const newFields = {
        form_id: newFormId,
        fields: formFields.fields,
        condition: formFields.condition,
      };

      const { error: insertFieldsError } = await supabase
        .from("automate_form_fields")
        .insert([newFields]);

      if (insertFieldsError) {
        console.error("Error cloning form fields:", insertFieldsError);
        return sendApiError(c, "Failed to clone form fields", 500);
      }
    }

    return sendApiResponse(c, { form_id: newFormId }, 201);
  } catch (err) {
    console.error("Clone Form Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
const deleteForm = async (c: Context) => {
  try {
    const user = c.get("user"); // Get authenticated user
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const formId = c.req.param("id");
    if (!formId) {
      return sendApiError(c, "Form ID is required", 400);
    }

    // Check if user can delete this form (only owner can delete)
    const canDelete = await canUserDeleteForm(user.user.id, formId);
    if (!canDelete) {
      return sendApiError(
        c,
        "Forbidden: You don't have permission to delete this form",
        403
      );
    }

    // Delete the form
    const { error: deleteError } = await supabase
      .from("automate_forms")
      .delete()
      .eq("id", formId);

    if (deleteError) {
      console.error("Delete Form Error:", deleteError);
      return sendApiError(c, "Failed to delete form", 500);
    }
    return sendApiResponse(c, { message: "Form deleted successfully" }, 200);
  } catch (err) {
    console.error("Delete Form Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
const createFormUsingTemplate = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const body = await c.req.json();
    console.log("Received template data:", body);

    if (!body.template_id) {
      return sendApiError(c, "Template ID is required", 400);
    }

    // Fetch template data
    const { data: template, error: templateError } = await supabase
      .from("automate_form_templates")
      .select("name, description,image_url, template_data")
      .eq("id", body.template_id)
      .single();

    if (templateError || !template) {
      console.log(templateError);
      return sendApiError(c, "Template not found", 404);
    }

    console.log("template");
    const templateData = template.template_data;

    if (!templateData) {
      return sendApiError(c, "Template data is missing or invalid", 400);
    }
     const { data: userdata, error: userDataerror } = await supabase
      .from("user_profile")
      .select("workspace_id")
      .eq("id", user.user.id)
      .single();
 if (userDataerror || !userdata) {
      return sendApiError(c, "Failed to retrieve user profile", 500);
    }
  // if (userdata.workspace_id) {
  //     const usageData = await UsageService.getWorkspaceUsage(
  //       userdata.workspace_id
  //     );
  //     if (usageData) {
  //       // Check if this form creation would exceed the limit
  //       if (
  //         usageData.usage.create_form_count >=
  //         usageData.limits.create_form_limit
  //       ) {
  //         return sendApiError(
  //           c,
  //           "Form creation limit reached. Please upgrade your plan to create more forms.",
  //           403
  //         );
  //       }
  //     }
  //   }
    // Create new form from template
    const newForm:any = {
      title: templateData.title || "Untitled Form",
      description: templateData.description || "",
      type: "templateform",
      heading: templateData.formheading || "",
      header_img: templateData.header_img || null,
      bg_image: templateData.bg_image || null,
      bg_color: templateData.bg_color || null,
      created_by: user.user.id,
      workspace_id: userdata.workspace_id
    };
     if (body.folder_id) {
      newForm.folder_id = body.folder_id;
    }

    const { data, error } = await supabase
      .from("automate_forms")
      .insert([newForm])
      .select("id");

    if (error || !data || data.length === 0) {
      console.error("Create Form Error:", error);
      return sendApiError(c, "Failed to create form", 500);
    }

    const formId = data[0].id;

    // Get user workspace
   

    if (userdata?.workspace_id) {
      // Track form creation in workspace usage
      await UsageService.incrementFormCreationCount(userdata.workspace_id);
    }

    // Insert default settings for the form
    const formSettings = {
      form_id: formId,
      is_public: true,
      accept_responses: true,
      thank_you_url: templateData.thank_you_url || null,
      thank_you_type: templateData.thank_you_type || "default",
      thank_you_data: templateData.thank_you_data || null,
    };

    const { error: settingsError } = await supabase
      .from("automate_form_settings")
      .insert([formSettings]);

    if (settingsError) {
      console.error("Create Form Settings Error:", settingsError);
      return sendApiError(c, "Form created, but failed to save settings", 500);
    }
    if (templateData.fields) {
      const newfileds = {
        form_id: formId,
        fields: templateData.fields,
      };
      const { error: fieldsError } = await supabase
        .from("automate_form_fields")
        .insert([newfileds]);

      if (fieldsError) {
        console.error("Create Form Fields Error:", fieldsError);
        return sendApiError(c, "Form created, but failed to save fields", 500);
      }
    }
    return sendApiResponse(c, { form_id: formId }, 201);
  } catch (err) {
    console.error("Create Form from Template Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
const createFormWithAI = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const { prompt,folder_id } = await c.req.json();
    if (!prompt) {
      return sendApiError(c, "Prompt is required", 400);
    }

    // Get user workspace
    const { data: userdata, error: userDataerror } = await supabase
      .from("user_profile")
      .select("workspace_id")
      .eq("id", user.user.id)
      .single();

    if (userDataerror || !userdata) {
      return sendApiError(c, "Failed to retrieve user profile", 500);
    }
 
    // Check if AI credits limit has been reached
    if (userdata.workspace_id) {
      const usageData = await UsageService.getWorkspaceUsage(
        userdata.workspace_id
      );
      if (usageData) {
        // Check if this AI form creation would exceed the credits limit
        if (
          usageData.usage.ai_credits_used >= usageData.limits.ai_credits_limit
        ) {
          return sendApiError(
            c,
            "AI credits limit reached for AI form creation. Please upgrade your plan to get more AI credits.",
            403
          );
        }
      }
    }

    // Call AI API to generate form
    const response = await fetch(
      "https://api.automatebusiness.com/functions/v1/Create_forms_with_ai",
      {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ prompt }),
      }
    );

    if (!response.ok) {
      return sendApiError(c, "Failed to generate form with AI", 500);
    }

    const aiFormData = await response.json();
    // Create form with AI-generated data
    const newForm:any = {
      title: aiFormData.form.title,
      description: aiFormData.form.description,
      type: aiFormData.form.type,
      heading: aiFormData.form.heading,
      bg_color: aiFormData.form.bg_color,
      heading_color: aiFormData.form.heading_color,
      description_color: aiFormData.form.description_color,
      font_family: aiFormData.form.font_family,
      created_by: user.user.id,
      workspace_id: userdata.workspace_id,
      ai_created: true,
    };
  if (folder_id) {
      newForm.folder_id =folder_id;
    }
    // Insert form
    const { data, error } = await supabase
      .from("automate_forms")
      .insert([newForm])
      .select("id");
    if (error || !data || data.length === 0) {
      console.error("Create Form Error:", error);
      return sendApiError(c, "Failed to create form", 500);
    }

    const formId = data[0].id;

    // Track form creation in workspace usage
    if (userdata.workspace_id) {
      await UsageService.incrementFormCreationCount(userdata.workspace_id);
    }

    // Track AI credits usage
    if (userdata.workspace_id) {
      // Assuming each AI form creation uses 1 credit
      await UsageService.updateAICreditsUsage(userdata.workspace_id, 1);
    }

    // Insert form settings
    const formSettings = {
      form_id: formId,
      is_public: aiFormData.setting.is_public,
      accept_responses: aiFormData.setting.accept_responses,
      thank_you_type: aiFormData.setting.thank_you_type,
      thank_you_data: aiFormData.setting.thank_you_data,
      thank_you_url: aiFormData.setting.thank_you_url,
    };

    const { error: settingsError } = await supabase
      .from("automate_form_settings")
      .insert([formSettings]);
    if (settingsError) {
      console.error("Create Form Settings Error:", settingsError);
      return sendApiError(c, "Form created, but failed to save settings", 500);
    }

    // Insert form fields
    if (aiFormData.form_fields && aiFormData.form_fields.length > 0) {
      // Process fields to add unique IDs to each field and their options
      const fieldsWithIds = aiFormData.form_fields.map((field: any) => {
        const fieldWithId = {
          ...field,
          id: uuidv4(),
        };

        // Handle dropdown fields (select type)
        if (
          field.type === "select" &&
          field.dropdownOptions &&
          Array.isArray(field.dropdownOptions)
        ) {
          fieldWithId.dropdownOptions = field.dropdownOptions.map(
            (option: any) => ({
              ...option,
              id: uuidv4(),
            })
          );
        }

        // Handle radio button fields
        if (
          field.type === "radio" &&
          field.radioOptions &&
          Array.isArray(field.radioOptions)
        ) {
          fieldWithId.radioOptions = field.radioOptions.map((option: any) => ({
            ...option,
            id: uuidv4(),
          }));
        }

        // Handle checkbox fields
        if (
          field.type === "checkbox" &&
          field.checkboxOptions &&
          Array.isArray(field.checkboxOptions)
        ) {
          fieldWithId.checkboxOptions = field.checkboxOptions.map(
            (option: any) => ({
              ...option,
              id: uuidv4(),
            })
          );
        }

        return fieldWithId;
      });

      const newFields = {
        form_id: formId,
        fields: fieldsWithIds,
      };

      const { error: fieldsError } = await supabase
        .from("automate_form_fields")
        .insert([newFields]);
      if (fieldsError) {
        console.error("Create Form Fields Error:", fieldsError);
        return sendApiError(c, "Form created, but failed to save fields", 500);
      }
    }
    const { data: createdForm } = await supabase
      .from("automate_forms")
      .select(
        `
    id,
    title,
    description,
    type,
    heading,
    header_img,
    bg_image,
    bg_color,
    heading_color,
    description_color,
    font_family,
    published,
    created_at,
    updated_at,
    created_by,
    workspace_id,
    automate_form_settings(
      is_public,
      accept_responses,
      thank_you_type,
      thank_you_data,
      thank_you_url
    ),
    automate_form_fields(fields)
  `
      )
      .eq("id", formId)
      .eq("is_deleted", false)
      .single();

    return sendApiResponse(
      c,
      {
        createdForm,
      },
      201
    );
  } catch (err) {
    console.error("Create Form with AI Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

const createFormWithvoice = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }
    const formData = await c.req.parseBody();
    const audioFile = formData.audio; 
    const folder_id=formData.folder_id;

    if (!audioFile) {
      return sendApiError(c, "Audio file is required", 400);
    }
    // Get user workspace
    const { data: userdata, error: userDataerror } = await supabase
      .from("user_profile")
      .select("workspace_id")
      .eq("id", user.user.id)
      .single();

    if (userDataerror || !userdata) {
      return sendApiError(c, "Failed to retrieve user profile", 500);
    }
 
    // Check if AI credits limit has been reached
    if (userdata.workspace_id) {
      const usageData = await UsageService.getWorkspaceUsage(
        userdata.workspace_id
      );
      if (usageData) {
        // Check if this AI form creation would exceed the credits limit
        if (
          usageData.usage.ai_credits_used >= usageData.limits.ai_credits_limit
        ) {
          return sendApiError(
            c,
            "AI credits limit reached for AI form creation. Please upgrade your plan to get more AI credits.",
            403
          );
        }
      }
    }
    const outgoingFormData = new FormData();
    outgoingFormData.append('audio', audioFile);
    // Call AI API to generate form
    const response = await fetch(
      "https://api.automatebusiness.com/functions/v1/create_form_with_speech",
      {
        method: "POST",
        body: outgoingFormData,
      }
    );

    if (!response.ok) {
      return sendApiError(c, "Failed to generate form with AI", 500);
    }

    const aiFormData = await response.json();
    // Create form with AI-generated data
    const newForm:any = {
      title: aiFormData.form.title,
      description: aiFormData.form.description,
      type: aiFormData.form.type,
      heading: aiFormData.form.heading,
      bg_color: aiFormData.form.bg_color,
      heading_color: aiFormData.form.heading_color,
      description_color: aiFormData.form.description_color,
      font_family: aiFormData.form.font_family,
      created_by: user.user.id,
      workspace_id: userdata.workspace_id,
      ai_created: true,
    };
  if (folder_id) {
      newForm.folder_id =folder_id;
    }
    // Insert form
    const { data, error } = await supabase
      .from("automate_forms")
      .insert([newForm])
      .select("id");
    if (error || !data || data.length === 0) {
      console.error("Create Form Error:", error);
      return sendApiError(c, "Failed to create form", 500);
    }

    const formId = data[0].id;

    // Track form creation in workspace usage
    if (userdata.workspace_id) {
      await UsageService.incrementFormCreationCount(userdata.workspace_id);
    }

    // Track AI credits usage
    if (userdata.workspace_id) {
      // Assuming each AI form creation uses 1 credit
      await UsageService.updateAICreditsUsage(userdata.workspace_id, 1);
    }

    // Insert form settings
    const formSettings = {
      form_id: formId,
      is_public: aiFormData.setting.is_public,
      accept_responses: aiFormData.setting.accept_responses,
      thank_you_type: aiFormData.setting.thank_you_type,
      thank_you_data: aiFormData.setting.thank_you_data,
      thank_you_url: aiFormData.setting.thank_you_url,
    };

    const { error: settingsError } = await supabase
      .from("automate_form_settings")
      .insert([formSettings]);
    if (settingsError) {
      console.error("Create Form Settings Error:", settingsError);
      return sendApiError(c, "Form created, but failed to save settings", 500);
    }

    // Insert form fields
    if (aiFormData.form_fields && aiFormData.form_fields.length > 0) {
      // Process fields to add unique IDs to each field and their options
      const fieldsWithIds = aiFormData.form_fields.map((field: any) => {
        const fieldWithId = {
          ...field,
          id: uuidv4(),
        };

        // Handle dropdown fields (select type)
        if (
          field.type === "select" &&
          field.dropdownOptions &&
          Array.isArray(field.dropdownOptions)
        ) {
          fieldWithId.dropdownOptions = field.dropdownOptions.map(
            (option: any) => ({
              ...option,
              id: uuidv4(),
            })
          );
        }

        // Handle radio button fields
        if (
          field.type === "radio" &&
          field.radioOptions &&
          Array.isArray(field.radioOptions)
        ) {
          fieldWithId.radioOptions = field.radioOptions.map((option: any) => ({
            ...option,
            id: uuidv4(),
          }));
        }

        // Handle checkbox fields
        if (
          field.type === "checkbox" &&
          field.checkboxOptions &&
          Array.isArray(field.checkboxOptions)
        ) {
          fieldWithId.checkboxOptions = field.checkboxOptions.map(
            (option: any) => ({
              ...option,
              id: uuidv4(),
            })
          );
        }

        return fieldWithId;
      });

      const newFields = {
        form_id: formId,
        fields: fieldsWithIds,
      };

      const { error: fieldsError } = await supabase
        .from("automate_form_fields")
        .insert([newFields]);
      if (fieldsError) {
        console.error("Create Form Fields Error:", fieldsError);
        return sendApiError(c, "Form created, but failed to save fields", 500);
      }
    }
    const { data: createdForm } = await supabase
      .from("automate_forms")
      .select(
        `
    id,
    title,
    description,
    type,
    heading,
    header_img,
    bg_image,
    bg_color,
    heading_color,
    description_color,
    font_family,
    published,
    created_at,
    updated_at,
    created_by,
    workspace_id,
    automate_form_settings(
      is_public,
      accept_responses,
      thank_you_type,
      thank_you_data,
      thank_you_url
    ),
    automate_form_fields(fields)
  `
      )
      .eq("id", formId)
      .eq("is_deleted", false)
      .single();

    return sendApiResponse(
      c,
      {
        createdForm,
      },
      201
    );
  } catch (err) {
    console.error("Create Form with AI Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

const copyFormToWorkspace = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const formId = c.req.param("formId");

    if (!formId) {
      return sendApiError(c, "Form ID is required", 400);
    }

    console.log("📢 Copy form request:", { formId });

    // Get user's current workspace
    const { data: userProfile, error: userProfileError } = await supabase
      .from("user_profile")
      .select("workspace_id")
      .eq("id", user.user.id)
      .single();

    if (userProfileError || !userProfile) {
      return sendApiError(c, "Failed to retrieve user profile", 500);
    }

    // Get source form
    const { data: sourceForm, error: sourceFormError } = await supabase
      .from("automate_forms")
      .select(`
        id, type, title, description, heading, header_img,
        bg_image, bg_color, heading_color, description_color,
        font_family, button_properties,
        automate_form_settings(
          is_public, accept_responses, thank_you_type, 
          thank_you_data, thank_you_url, copy_allowed
        )
      `)
      .eq("id", formId)
      .eq("is_deleted", false)
      .single();

    if (sourceFormError || !sourceForm) {
      return sendApiError(c, "Source form not found", 404);
    }

    const settings = sourceForm.automate_form_settings?.[0];
    if (!settings?.copy_allowed) {
      return sendApiError(c, "Copying this form is not allowed", 403);
    }

    // Create new form
    const newForm = {
      type: sourceForm.type,
      title: sourceForm.title ? `${sourceForm.title} (Copy)` : null,
      description: sourceForm.description,
      heading: sourceForm.heading,
      header_img: sourceForm.header_img,
      bg_image: sourceForm.bg_image,
      bg_color: sourceForm.bg_color,
      heading_color: sourceForm.heading_color,
      description_color: sourceForm.description_color,
      font_family: sourceForm.font_family,
      button_properties: sourceForm.button_properties,
      created_by: user.user.id,
      workspace_id: userProfile.workspace_id
    };

    const { data: newFormData, error: newFormError } = await supabase
      .from("automate_forms")
      .insert([newForm])
      .select("id")
      .single();

    if (newFormError || !newFormData) {
      console.error("Create Form Error:", newFormError);
      return sendApiError(c, "Failed to copy form", 500);
    }

    const newFormId = newFormData.id;
     await UsageService.incrementFormCreationCount(userProfile.workspace_id);
    // Copy settings
    const formSettings = {
      form_id: newFormId,
      is_public: settings?.is_public ?? true,
      accept_responses: settings?.accept_responses ?? true,
      thank_you_type: settings?.thank_you_type ?? null,
      thank_you_data: settings?.thank_you_data ?? null,
      thank_you_url: settings?.thank_you_url ?? null,
    };

    const { error: settingsError } = await supabase
      .from("automate_form_settings")
      .insert([formSettings]);

    if (settingsError) {
      console.error("Create Form Settings Error:", settingsError);
      return sendApiError(c, "Form copied, but failed to save settings", 500);
    }

    // Copy fields
    const { data: sourceFormFields, error: fieldsError } = await supabase
      .from("automate_form_fields")
      .select("fields, condition")
      .eq("form_id", formId)
      .single();

    if (fieldsError) {
      console.error("Error retrieving form fields:", fieldsError);
      return sendApiError(c, "Form copied, but failed to fetch fields", 500);
    }

    if (sourceFormFields) {
      const newFields = {
        form_id: newFormId,
        fields: sourceFormFields.fields,
        condition: sourceFormFields.condition,
      };

      const { error: insertFieldsError } = await supabase
        .from("automate_form_fields")
        .insert([newFields]);

      if (insertFieldsError) {
        console.error("Error copying form fields:", insertFieldsError);
        return sendApiError(c, "Form copied, but failed to copy fields", 500);
      }
    }
    return sendApiResponse(
      c,
      {
        form_id: newFormId,
        message: "Form successfully copied to target workspace",
      },
      201
    );
  } catch (err) {
    console.error("Copy Form to Workspace Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

// Get all trashed forms for the user
const gettrash = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }
    console.log(user.user.id);
    const { data, error } = await supabase
      .from("automate_forms")
      .select("*")
      .eq("created_by", user.user.id)
      .eq("is_deleted", true);
      console.log(data);
    if (error) {
      return sendApiError(c, "Failed to fetch trashed forms", 500);
    }
    return sendApiResponse(c, { forms: data || [] }, 200);
  } catch (err) {
    return sendApiError(c, "Internal server error", 500);
  }
  // return sendApiError(c, "Internal server error", 500);
};

// Restore a trashed form
const restoreForm = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }
    const formId = c.req.param("id");
    if (!formId) {
      return sendApiError(c, "Form ID is required", 400);
    }
    // Check if the form exists and belongs to the user
    const { data: form, error: fetchError } = await supabase
      .from("automate_forms")
      .select("id, created_by")
      .eq("id", formId)
      .eq("is_deleted", true)
      .single();
    if (fetchError || !form) {
      return sendApiError(c, "Trashed form not found", 404);
    }
    if (form.created_by !== user.user.id) {
      return sendApiError(c, "Forbidden: You don't have permission to restore this form", 403);
    }
    // Restore the form
    const { error: restoreError } = await supabase
      .from("automate_forms")
      .update({ is_deleted: false })
      .eq("id", formId);
    if (restoreError) {
      return sendApiError(c, "Failed to restore form", 500);
    }
    return sendApiResponse(c, { message: "Form restored successfully" }, 200);
  } catch (err) {
    return sendApiError(c, "Internal server error", 500);
  }
};
const moveToTrash = async (c: Context) => {
  try {
    const user = c.get("user"); // Get authenticated user
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const formId = c.req.param("id");
    if (!formId) {
      return sendApiError(c, "Form ID is required", 400);
    }

    // Check if the form exists and belongs to the user
    const { data: form, error: fetchError } = await supabase
      .from("automate_forms")
      .select("id, created_by, workspace_id")
      .eq("id", formId)
      .eq("is_deleted", false)
      .single();

    if (fetchError) {
      console.error("Fetch Form Error:", fetchError);
      return sendApiError(c, "Form not found", 404);
    }

    if (form.created_by !== user.user.id) {
      return sendApiError(
        c,
        "Forbidden: You don't have permission to delete this form",
        403
      );
    }

    // Mark the form as deleted instead of actually deleting it
    const { error: deleteError } = await supabase
      .from("automate_forms")
      .update({ is_deleted: true })
      .eq("id", formId);

    if (deleteError) {
      console.error("Delete Form Error:", deleteError);
      return sendApiError(c, "Failed to move form to trash", 500);
    }
    return sendApiResponse(c, { message: "Form moved to trash successfully" }, 200);
  } catch (err) {
    console.error("Delete Form Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};
const addSummaryColumn = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const body = await c.req.json();
    const { formId, fieldName, prompt } = body;

    if (!formId || !fieldName || !prompt) {
      return sendApiError(c, "Missing formId, fieldName, or prompt", 400);
    }

    // Check if the form exists and user has access
    const { data: form, error: formError } = await supabase
      .from("automate_forms")
      .select("id, created_by")
      .eq("id", formId)
      .eq("is_deleted", false)
      .single();

    if (formError || !form) {
      return sendApiError(c, "Form not found", 404);
    }
  

    // Insert the summary column configuration
    const { error: insertError } = await supabase
      .from("automate_form_field_summary_prompts")
      .insert([
        {
          form_id: formId,
          field_name: fieldName,
          prompt: prompt
        }
      ]);

    if (insertError) {
      console.error("Add Summary Column Error:", insertError);
      return sendApiError(c, "Failed to add summary column", 500);
    }

    // Update automate_form_fields.fields array
    const { data: formFieldsRow, error: fieldsFetchError } = await supabase
      .from("automate_form_fields")
      .select("fields")
      .eq("form_id", formId)
      .single();

    if (fieldsFetchError || !formFieldsRow) {
      return sendApiError(c, "Form fields not found", 404);
    }

    const fields = Array.isArray(formFieldsRow.fields) ? formFieldsRow.fields : [];
    // Add new summary column field
    const newField = {
      id: uuidv4(),
      name: "analysis",
      type: "analysis",
      isRequired: false,
      title: fieldName,
      fieldName: fieldName,
      isHide: false,
      isDisable: false
    };
    fields.push(newField);

    const { error: updateFieldsError } = await supabase
      .from("automate_form_fields")
      .update({ fields })
      .eq("form_id", formId);

    if (updateFieldsError) {
      console.error("Update automate_form_fields Error:", updateFieldsError);
      return sendApiError(c, "Failed to update form fields with summary column", 500);
    }

    // Fetch all response IDs for the form
    const { data: responses, error: responsesError } = await supabase
      .from("automate_form_responses")
      .select("id")
      .eq("form_id", formId);

    if (responsesError) {
      console.error("Fetch responses error:", responsesError);
      // Optionally: return error or just log and continue
    }

    // Prepare job queue inserts
    if (responses && responses.length > 0) {
      const jobs = responses.map((resp: any) => ({
        response_id: resp.id,
        form_id: formId,
        field_name: fieldName,
        prompt: prompt,
      }));

      // Insert jobs in batches (if needed)
      const { error: jobInsertError } = await supabase
        .from("automate_form_summary_job_queue")
        .insert(jobs);

      if (jobInsertError) {
        console.error("Job queue insert error:", jobInsertError);
        // Optionally: return error or just log and continue
      }
    }

    return sendApiResponse(c, { message: "Summary column added successfully" }, 201);
  } catch (err) {
    console.error("Add Summary Column Unexpected Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

export {
  createForm,
  updateForm,
  getUserForms,
  publishForm,
  searchForms,
  getPublicForm,
  getForm,
  cloneForm,
  deleteForm,
  createFormUsingTemplate,
  createFormWithAI,
  copyFormToWorkspace,
  gettrash,
  restoreForm,
  moveToTrash,
  createFormWithvoice,
  addSummaryColumn
};
