import { <PERSON>o } from "hono";
import { verifySupabaseAuth } from "../middleware/auth.middleware";
import { 
  getActionforGmail,
  getUserIntegrationCredentials,
  addGmailConnection,
  getProfile,
  testEmail,
  linkFormToGmail
} from "../controllers/gmail.controllers";

const gmail = new Hono();

// Get actions for Gmail integration
gmail.get("/action/:id", verifySupabaseAuth, getActionforGmail);

// Get user's Gmail credentials
gmail.get("/connection/:id", verifySupabaseAuth, getUserIntegrationCredentials);

// OAuth flow - initiate Gmail connection
gmail.get('/addconnection', verifySupabaseAuth, addGmailConnection);

// Get Gmail profile
gmail.post("/profile", verifySupabaseAuth, getProfile);

// Test email sending
gmail.post("/test", verifySupabaseAuth, testEmail);

// Link form to Gmail
gmail.post("/linkform", verifySupabaseAuth, linkFormToGmail);

export { gmail };
