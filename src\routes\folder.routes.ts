
import { Hono } from "hono";
import {createFolder,addFormToFolder,getFormsInFolder,getUserFoldersWithForms, deleteFolders,removeFormsFromFolder,getTrashedFolders, restoreFolder,deleteFoldersfromTrash} from "../controllers/folder.controllers"
import { verifySupabaseAuth } from "../middleware/auth.middleware";
const folder = new Hono();
folder.post("/create",verifySupabaseAuth,createFolder)
folder.post("/addform",verifySupabaseAuth,addFormToFolder)
folder.get("/:folder_id",verifySupabaseAuth,getFormsInFolder)
folder.get("/",verifySupabaseAuth,getUserFoldersWithForms)
folder.delete("/",verifySupabaseAuth,deleteFolders)
folder.delete("/removeform",verifySupabaseAuth,removeFormsFromFolder)
folder.post("/getTrashedFolders",verifySupaba<PERSON>Auth,getTrashedFolders)
folder.post("/restore/:id", verifySupabaseAuth, restoreFolder);
folder.delete("/deleteFoldersfromTrash",verifySupabaseAuth,deleteFoldersfromTrash)
export {folder};