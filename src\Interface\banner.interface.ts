export interface CreateBannerRequest {
  name: string;
  category: string;
  banner: File;
}

export interface UpdateBannerRequest {
  name?: string;
  category?: string;
  banner?: File;
}

export interface BannerResponse {
  id: string;
  name: string;
  category: string;
  banner_url: string;
  created_at: string;
  updated_at?: string;
  created_by: string;
}

export interface BannersGroupedByCategory {
  category: string;
  banners: BannerResponse[];
}

export interface GetBannersResponse {
  banners: BannerResponse[];
  pagination: {
    current_page: number;
    total_pages: number;
    total_count: number;
    limit: number;
  };
}

export interface GetBannersGroupedResponse {
  banners_by_category: BannersGroupedByCategory[];
}

export interface GetBannerCategoriesResponse {
  categories: string[];
}
