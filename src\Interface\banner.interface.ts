export interface CreateBannerRequest {
  name: string;
  url?: string;
  title?: string;
  description?: string;
  banner: File;
}

export interface UpdateBannerRequest {
  name?: string;
  url?: string;
  title?: string;
  description?: string;
  banner?: File;
}

export interface BannerResponse {
  id: string;
  name: string;
  url?: string;
  title?: string;
  description?: string;
  banner_url: string;
  created_at: string;
  updated_at?: string;
  created_by: string;
}

export interface GetBannersResponse {
  banners: BannerResponse[];
  pagination: {
    current_page: number;
    total_pages: number;
    total_count: number;
    limit: number;
  };
}
