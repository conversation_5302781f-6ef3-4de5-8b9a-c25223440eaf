import type { Context } from "hono";
import { supabase } from "../db";
import { sendApiError, sendApiResponse } from "../utils/Response";
import { upload } from "../utils/upload";
import { BUCKET_COMMON } from "../constant";
import type {
  CreateTicketRequest
} from "../Interface/support.interface";



const createTicket = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const body: CreateTicketRequest = await c.req.json();
    const {
      subject,
      description,
      category,
      sub_category,
      video_link,
      voice_link,
      image_links,
    } = body;

    // Validate required fields
    if (!subject || !description || !category || !sub_category) {
      return sendApiError(c, "Subject, description, category, sub_category, and priority are required", 400);
    }


    // Validate URLs if provided
    if (video_link && !isValidUrl(video_link)) {
      return sendApiError(c, "Invalid video link URL", 400);
    }

    if (voice_link && !isValidUrl(voice_link)) {
      return sendApiError(c, "Invalid voice link URL", 400);
    }
    if (image_links) {
      for (const link of image_links) {
        if (!isValidUrl(link)) {
          return sendApiError(c, "Invalid image link URL", 400);
        }
      }
    }

    // Create the ticket
    const { data: ticket, error: ticketError } = await supabase
      .from("support_tickets")
      .insert([
        {
          subject: subject.trim(),
          description: description.trim(),
          category,
          sub_category,
          status: "Pending", // Default status
          created_by: user.user.id,
          video_link: video_link || null,
          voice_link: voice_link || "",
          image_links: image_links || [],
          updated_at: new Date().toISOString(),
          app:5,
          priority:"Normal"
        }
      ])
      .select(`
        id
      `)
      .single();

    if (ticketError) {
      console.error("Create Ticket Error:", ticketError);
      return sendApiError(c, "Failed to create support ticket", 500);
    }

    return sendApiResponse(c, {
      message: "Support ticket created successfully",
      ticket: {
        ticket_id: `TICKET-${ticket.id}`
      }
    });

  } catch (err) {
    console.error("Create Ticket Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

const getUserTickets = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const { status, category, priority, page = "1", limit = "10" } = c.req.query();

    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const offset = (pageNum - 1) * limitNum;

    let query = supabase
      .from("support_tickets")
      .select(`
        id,
        created_at,
        subject,
        description,
        category,
        sub_category,
        image_links,
        video_link,
        voice_link,
        status,
        priority,
        referred_to,
        updated_at,
        assigned_to_email
      `, { count: "exact" })
      .eq("created_by", user.user.id)
      .order("created_at", { ascending: false });

    // Apply filters
    if (status) {
      query = query.eq("status", status);
    }

    if (category) {
      query = query.eq("category", category);
    }

    if (priority) {
      query = query.eq("priority", priority);
    }

    query = query.range(offset, offset + limitNum - 1);

    const { data: tickets, error: ticketsError, count } = await query;

    if (ticketsError) {
      console.error("Get Tickets Error:", ticketsError);
      return sendApiError(c, "Failed to retrieve tickets", 500);
    }

    const formattedTickets = tickets?.map(ticket => ({
      ...ticket,
      ticket_id: `TICKET-${ticket.id}`,
    })) || [];

    return sendApiResponse(c, {
      tickets: formattedTickets,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: count || 0,
        total_pages: Math.ceil((count || 0) / limitNum)
      }
    });

  } catch (err) {
    console.error("Get User Tickets Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

// Helper functions
const isValidUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

const getTicketById = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const ticketId = c.req.param("id");
    if (!ticketId) {
      return sendApiError(c, "Ticket ID is required", 400);
    }

    const { data: ticket, error: ticketError } = await supabase
      .from("support_tickets")
      .select(`
        id,
        created_at,
        subject,
        description,
        category,
        sub_category,
        image_links,
        video_link,
        voice_link,
        status,
        priority,
        referred_to,
        updated_at,
        assigned_to_email,
        user_profile!support_tickets_created_by_fkey (
          first_name,
          last_name,
          email,
          profile_image
        )
      `)
      .eq("id", ticketId)
      .eq("created_by", user.user.id)
      .single();

    if (ticketError || !ticket) {
      return sendApiError(c, "Ticket not found or access denied", 404);
    }

    return sendApiResponse(c, {
      ticket: {
        ...ticket,
        ticket_id: `TICKET-${ticket.id}`,
      }
    });

  } catch (err) {
    console.error("Get Ticket By ID Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

const updateTicket = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const ticketId = c.req.param("id");
    if (!ticketId) {
      return sendApiError(c, "Ticket ID is required", 400);
    }

    const body = await c.req.json();
    const { subject, description } = body;

    // Verify ticket exists and belongs to user
    const { data: existingTicket, error: ticketError } = await supabase
      .from("support_tickets")
      .select("id, status")
      .eq("id", ticketId)
      .eq("created_by", user.user.id)
      .single();

    if (ticketError || !existingTicket) {
      return sendApiError(c, "Ticket not found or access denied", 404);
    }

    // Don't allow updates to closed tickets
    if (existingTicket.status === "closed") {
      return sendApiError(c, "Cannot update closed tickets", 400);
    }

    // Prepare update data
    const updateData: any = {
      updated_at: new Date().toISOString()
    };

    if (subject) {
      updateData.subject = subject.trim();
    }

    if (description) {
      updateData.description = description.trim();
    }


    // Update the ticket
    const { data: updatedTicket, error: updateError } = await supabase
      .from("support_tickets")
      .update(updateData)
      .eq("id", ticketId)
      .select(`
        id,
        created_at,
        subject,
        description,
        category,
        sub_category,
        image_links,
        video_link,
        voice_link,
        status,
        priority,
        referred_to,
        updated_at,
        assigned_to_email
      `)
      .single();

    if (updateError) {
      console.error("Update Ticket Error:", updateError);
      return sendApiError(c, "Failed to update ticket", 500);
    }

    return sendApiResponse(c, {
      message: "Ticket updated successfully",
      ticket: {
        ...updatedTicket,
        ticket_id: `TICKET-${updatedTicket.id}`
      }
    });

  } catch (err) {
    console.error("Update Ticket Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

const deleteTicket = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const ticketId = c.req.param("id");
    if (!ticketId) {
      return sendApiError(c, "Ticket ID is required", 400);
    }

    // Verify ticket exists and belongs to user
    const { data: existingTicket, error: ticketError } = await supabase
      .from("support_tickets")
      .select("id, status")
      .eq("id", ticketId)
      .eq("created_by", user.user.id)
      .single();

    if (ticketError || !existingTicket) {
      return sendApiError(c, "Ticket not found or access denied", 404);
    }

    // Don't allow deletion of tickets that are being processed
    if (existingTicket.status === "In Progress") {
      return sendApiError(c, "Cannot delete tickets that are currently being processed", 400);
    }

    // Delete the ticket
    const { error: deleteError } = await supabase
      .from("support_tickets")
      .delete()
      .eq("id", ticketId);

    if (deleteError) {
      console.error("Delete Ticket Error:", deleteError);
      return sendApiError(c, "Failed to delete ticket", 500);
    }

    return sendApiResponse(c, {
      message: "Ticket deleted successfully"
    }, 200);

  } catch (err) {
    console.error("Delete Ticket Error:", err);
    return sendApiError(c, "Internal server error", 500);
  }
};

const sendChatMessage = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const ticketId = c.req.param("id");
    if (!ticketId) {
      return sendApiError(c, "Ticket ID is required", 400);
    }

    const body = await c.req.json();
    const { comment, image_links, video_link, voice_link } = body;

    // Validate required fields
    if (!comment) {
      return sendApiError(c, "Comment is required", 400);
    }

    // Verify ticket exists and belongs to user
    const { data: existingTicket, error: ticketError } = await supabase
      .from("support_tickets")
      .select("id, status")
      .eq("id", ticketId)
      .eq("created_by", user.user.id)
      .single();

    if (ticketError || !existingTicket) {
      return sendApiError(c, "Ticket not found or access denied", 404);
    }

    // Don't allow messages on closed tickets
    if (existingTicket.status === "Closed") {
      return sendApiError(c, "Cannot send messages to closed tickets", 400);
    }

    if (voice_link && !isValidUrl(voice_link)) {
      return sendApiError(c, "Invalid voice link URL", 400);
    }

    // Validate URLs if provided
    if (video_link && !isValidUrl(video_link)) {
      return sendApiError(c, "Invalid video link URL", 400);
    }

    if (image_links) {
      for (const link of image_links) {
        if (!isValidUrl(link)) {
          return sendApiError(c, "Invalid image link URL", 400);
        }
      }
    }

    // Insert message into ticket_logs
    const { data: logEntry, error: logError } = await supabase
      .from("ticket_logs")
      .insert({
        ticket_id: parseInt(ticketId),
        comment: comment.trim(),
        status: 'Client Reply',
        image_links: image_links || [],
        reply_by: user.user.id,
        video_link: video_link || null,
        voice_link: voice_link || null
      })
      .select()
      .single();

    if (logError) {
      console.error("Send Chat Message Error:", logError);
      return sendApiError(c, "Failed to send message", 500);
    }
    return sendApiResponse(c, {
      message: "Message sent successfully",
      log_entry: logEntry
    });

  } catch (error) {
    console.error("Send Chat Message Error:", error);
    return sendApiError(c, "Internal server error", 500);
  }
};

// Add a function to get ticket messages
const getTicketMessages = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const ticketId = c.req.param("id");
    if (!ticketId) {
      return sendApiError(c, "Ticket ID is required", 400);
    }

    // Verify ticket exists and belongs to user
    const { data: existingTicket, error: ticketError } = await supabase
      .from("support_tickets")
      .select("id")
      .eq("id", ticketId)
      .eq("created_by", user.user.id)
      .single();

    if (ticketError || !existingTicket) {
      return sendApiError(c, "Ticket not found or access denied", 404);
    }

    // Get all messages for this ticket
    const { data: messages, error: messagesError } = await supabase
      .from("ticket_logs")
      .select(`
        id,
        created_at,
        comment,
        status,
        image_links,
        video_link,
        voice_link,
        reply_by,
        user_profile:reply_by (
          first_name,
          last_name,
          email,
          profile_image
        )
      `)
      .eq("ticket_id", ticketId)
      .order("created_at", { ascending: true });

    if (messagesError) {
      console.error("Get Ticket Messages Error:", messagesError);
      return sendApiError(c, "Failed to retrieve ticket messages", 500);
    }

    return sendApiResponse(c, {
      messages: messages || []
    });

  } catch (error) {
    console.error("Get Ticket Messages Error:", error);
    return sendApiError(c, "Internal server error", 500);
  }
};

export {
  createTicket,
  getUserTickets,
  getTicketById,
  updateTicket,
  deleteTicket,
  sendChatMessage,
  getTicketMessages
};


