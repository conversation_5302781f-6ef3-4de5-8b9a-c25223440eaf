import type { Context } from "hono";
import { supabase } from "../db";
import { sendApiError, sendApiResponse } from "../utils/Response";

// Get all sub-categories from automate_form_sub_categories table
const getAllSubCategories = async (c: Context) => {
  try {
    const { data, error } = await supabase
      .from("automate_form_sub_categories")
      .select("id, name");
    if (error) return sendApiError(c, "Failed to fetch sub categories", 500);
    return sendApiResponse(c, { sub_categories: data }, 200);
  } catch (err) {
    return sendApiError(c, "Internal server error", 500);
  }
};
// Get all published tutorials (optionally filter by category or app)
const getTutorials = async (c: Context) => {
  try {
    const { sub_category_id, app } = c.req.query();

    if(!sub_category_id && !app ){
        return sendApiError(c, "Missing required fields", 400); 
    }
    const { data, error } = await supabase
      .from("tutorials")
      .select(
        "id,created_at,title,description,video_url,thumbnail_link,published,updated_at,lessson_sequence"
      )
      .eq("published", true)
      .eq("sub_category_id", sub_category_id)
      .eq("app", app);
    console.log("error", error);
    if (error) return sendApiError(c, "Failed to fetch tutorials", 500);
    return sendApiResponse(c, { tutorials: data }, 200);
  } catch (err) {
    return sendApiError(c, "Internal server error", 500);
  }
};

// Get a single tutorial by id
const getTutorialById = async (c: Context) => {
  try {
    const id = c.req.param("id");
    const { data, error } = await supabase
      .from("tutorials")
      .select("*")
      .eq("id", id)
      .single();
    if (error || !data) return sendApiResponse(c, { tutorial: [] }, 200);
    return sendApiResponse(c, { tutorial: data }, 200);
  } catch (err) {
    return sendApiError(c, "Internal server error", 500);
  }
};

// Create a new tutorial
const createTutorial = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }
    const body = await c.req.json();
    const {
      title,
      description,
      category,
      sub_category_id,
      video_url,
      thumbnail_link,
      published,
      lessson_sequence,
      app,
    } = body;
    if (
      !title ||
      !description ||
      !category ||
      !video_url ||
      !thumbnail_link ||
      !sub_category_id ||
      lessson_sequence === undefined
    ) {
      return sendApiError(c, "Missing required fields", 400);
    }
    const { data, error } = await supabase
      .from("tutorials")
      .insert([
        {
          title,
          description,
          category,
          video_url,
          thumbnail_link,
          published: !!published,
          lessson_sequence,
          sub_category_id: sub_category_id,
          app,
        },
      ])
      .select()
      .single();
    if (error) return sendApiError(c, "Failed to create tutorial", 500);
    return sendApiResponse(c, { tutorial: data }, 201);
  } catch (err) {
    return sendApiError(c, "Internal server error", 500);
  }
};

// Update a tutorial
const updateTutorial = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }

    const id = c.req.param("id");
    const body = await c.req.json();

    const {
      title,
      description,
      video_url,
      thumbnail_link,
      published,
      lessson_sequence,
      app,
      sub_category_id   
    } = body;

    const updateData: Record<string, any> = {
      updated_at: new Date().toISOString(),
    };

    if (title !== undefined) updateData.title = title;
    if (description !== undefined) updateData.description = description;
    if (sub_category_id !== undefined) updateData.sub_category_id = sub_category_id;
    if (video_url !== undefined) updateData.video_url = video_url;
    if (thumbnail_link !== undefined) updateData.thumbnail_link = thumbnail_link;
    if (published !== undefined) updateData.published = published;
    if (lessson_sequence !== undefined) updateData.lessson_sequence = lessson_sequence;
    if (app !== undefined) updateData.app = app;

    const { data, error } = await supabase
      .from("tutorials")
      .update(updateData)
      .eq("id", id)
      .select()
      .single();
    if (error || !data) {
        console.log("error",error)
      return sendApiError(c, "Failed to update tutorial", 500);
    }

    return sendApiResponse(c, { tutorial: data }, 200);
  } catch (err) {
    return sendApiError(c, "Internal server error", 500);
  }
};


// Delete a tutorial
const deleteTutorial = async (c: Context) => {
  try {
    const user = c.get("user");
    if (!user) {
      return sendApiError(c, "Unauthorized", 401);
    }
    const id = c.req.param("id");
    const { error } = await supabase.from("tutorials").delete().eq("id", id);
    if (error) return sendApiError(c, "Failed to delete tutorial", 500);
    return sendApiResponse(c, { message: "Tutorial deleted" }, 200);
  } catch (err) {
    return sendApiError(c, "Internal server error", 500);
  }
};

export {
  getTutorials,
  getTutorialById,
  createTutorial,
  updateTutorial,
  deleteTutorial,
  getAllSubCategories,
};
