import { Hono } from "hono";
import {createForm,updateForm,getUserForms,publishForm,searchForms,getPublicForm,getForm,cloneForm,deleteForm, createFormUsingTemplate, createFormWithAI, copyFormToWorkspace,moveToTrash,gettrash,restoreForm,createFormWithvoice, addSummaryColumn} from "../controllers/form.controllers"
import { verifySupabaseAuth } from "../middleware/auth.middleware";
const form = new Hono();
form.post("/createform",verifySupabaseAuth,createForm);
form.put("/updateform/:id",verifySupabaseAuth,updateForm);
form.get("/",verifySupabaseAuth,getUserForms);
form.put("/publish/:id",verifySupabaseAuth,publishForm);
form.get("/search",verifySupabaseAuth, searchForms);
form.get("/public/:id",getPublicForm);
form.get("/:id",verifySupabaseAuth,getForm);
form.get("clone/:id",verifySupabaseAuth,cloneForm);
form.delete("/:id", verifySupabaseAuth, deleteForm);
form.post("/createform/template",verifySupabaseAuth,createFormUsingTemplate);
form.post("/create-with-ai", verifySupabaseAuth, createFormWithAI);
form.post("/create-with-voice", verifySupabaseAuth, createFormWithvoice);
form.get("/copy-to-workspace/:formId", verifySupabaseAuth, copyFormToWorkspace);
form.delete("/trash/:id", verifySupabaseAuth, moveToTrash);
form.post("/trashforms", verifySupabaseAuth, gettrash);
form.post("/restore/:id", verifySupabaseAuth, restoreForm);
form.post("/add-summary-column", verifySupabaseAuth, addSummaryColumn);

export {form};
