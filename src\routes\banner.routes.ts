import { Hono } from "hono";
import {
  createBanner,
  getBanners,
  getBannerById,
  updateBanner,
  deleteBanner,
  getBannersGroupedByCategory,
  getBannerCategories
} from "../controllers/banner.controllers";
import { verifySupabaseAuth } from "../middleware/auth.middleware";

const banner = new Hono();

// Apply auth middleware to all routes
banner.use("*", verifySupabaseAuth);

// Banner CRUD routes
banner.post("/", createBanner);                           // POST /v1/banners - Create banner
banner.get("/", getBanners);                              // GET /v1/banners - Get all banners with pagination and category filter
banner.get("/categories", getBannerCategories);          // GET /v1/banners/categories - Get unique categories
banner.get("/grouped", getBannersGroupedByCategory);     // GET /v1/banners/grouped - Get banners grouped by category
banner.get("/:id", getBannerById);                       // GET /v1/banners/:id - Get banner by ID
banner.put("/:id", updateBanner);                        // PUT /v1/banners/:id - Update banner
banner.delete("/:id", deleteBanner);                     // DELETE /v1/banners/:id - Delete banner

export { banner };
