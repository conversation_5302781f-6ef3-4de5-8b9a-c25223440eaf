import { Hono } from "hono";
import { getTutorials, getTutorialById, createTutorial, updateTutorial, deleteTutorial, getAllSubCategories } from "../controllers/tutorials.controllers";
import { verifySupabaseAuth } from "../middleware/auth.middleware";
const tutorial = new Hono();

tutorial.get("/sub_category",getAllSubCategories)
// Get all published tutorials (optionally filter by category or app)
tutorial.get("/", getTutorials);
// Get a single tutorial by id
tutorial.get("/:id", getTutorialById);
// Create a new tutorial
tutorial.post("/",verifySupabaseAuth, createTutorial);
// Update a tutorial
tutorial.put("/:id",verifySupabaseAuth, updateTutorial);
// Delete a tutorial
tutorial.delete("/:id",verifySupabaseAuth, deleteTutorial);

export {tutorial}
