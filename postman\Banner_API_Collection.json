{
  "info": {
    "name": "Banner Management API",
    "description": "Complete CRUD operations for banner management with file upload",
    "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
  },
  "variable": [
    {
      "key": "baseUrl",
      "value": "http://localhost:3000",
      "type": "string"
    },
    {
      "key": "authToken",
      "value": "YOUR_AUTH_TOKEN_HERE",
      "type": "string"
    },
    {
      "key": "bannerId",
      "value": "",
      "type": "string"
    }
  ],
  "item": [
    {
      "name": "Create Banner",
      "request": {
        "method": "POST",
        "header": [
          {
            "key": "Authorization",
            "value": "Bearer {{authToken}}",
            "type": "text"
          }
        ],
        "body": {
          "mode": "formdata",
          "formdata": [
            {
              "key": "name",
              "value": "Summer Sale Banner",
              "type": "text"
            },
            {
              "key": "url",
              "value": "https://example.com/sale",
              "type": "text"
            },
            {
              "key": "title",
              "value": "Summer Sale",
              "type": "text"
            },
            {
              "key": "description",
              "value": "Get 50% off on all items",
              "type": "text"
            },
            {
              "key": "banner",
              "type": "file",
              "src": []
            }
          ]
        },
        "url": {
          "raw": "{{baseUrl}}/v1/banners",
          "host": ["{{baseUrl}}"],
          "path": ["v1", "banners"]
        }
      },
      "event": [
        {
          "listen": "test",
          "script": {
            "exec": [
              "if (pm.response.code === 201) {",
              "    const response = pm.response.json();",
              "    pm.collectionVariables.set('bannerId', response.data.banner.id);",
              "}"
            ]
          }
        }
      ]
    },
    {
      "name": "Get All Banners",
      "request": {
        "method": "GET",
        "header": [
          {
            "key": "Authorization",
            "value": "Bearer {{authToken}}",
            "type": "text"
          }
        ],
        "url": {
          "raw": "{{baseUrl}}/v1/banners?page=1&limit=10",
          "host": ["{{baseUrl}}"],
          "path": ["v1", "banners"],
          "query": [
            {
              "key": "page",
              "value": "1"
            },
            {
              "key": "limit",
              "value": "10"
            },

          ]
        }
      }
    },
    {
      "name": "Get Banner by ID",
      "request": {
        "method": "GET",
        "header": [
          {
            "key": "Authorization",
            "value": "Bearer {{authToken}}",
            "type": "text"
          }
        ],
        "url": {
          "raw": "{{baseUrl}}/v1/banners/{{bannerId}}",
          "host": ["{{baseUrl}}"],
          "path": ["v1", "banners", "{{bannerId}}"]
        }
      }
    },
    {
      "name": "Update Banner",
      "request": {
        "method": "PUT",
        "header": [
          {
            "key": "Authorization",
            "value": "Bearer {{authToken}}",
            "type": "text"
          }
        ],
        "body": {
          "mode": "formdata",
          "formdata": [
            {
              "key": "name",
              "value": "Updated Summer Sale Banner",
              "type": "text"
            },
            {
              "key": "url",
              "value": "https://example.com/updated-sale",
              "type": "text"
            },
            {
              "key": "title",
              "value": "Updated Summer Sale",
              "type": "text"
            },
            {
              "key": "description",
              "value": "Updated description for summer sale",
              "type": "text"
            },
            {
              "key": "banner",
              "type": "file",
              "src": [],
              "disabled": true
            }
          ]
        },
        "url": {
          "raw": "{{baseUrl}}/v1/banners/{{bannerId}}",
          "host": ["{{baseUrl}}"],
          "path": ["v1", "banners", "{{bannerId}}"]
        }
      }
    },
    {
      "name": "Delete Banner",
      "request": {
        "method": "DELETE",
        "header": [
          {
            "key": "Authorization",
            "value": "Bearer {{authToken}}",
            "type": "text"
          }
        ],
        "url": {
          "raw": "{{baseUrl}}/v1/banners/{{bannerId}}",
          "host": ["{{baseUrl}}"],
          "path": ["v1", "banners", "{{bannerId}}"]
        }
      }
    }
  ]
}
