{"info": {"name": "Banner Management API", "description": "Complete CRUD operations for banner management with file upload", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "type": "string"}, {"key": "authToken", "value": "YOUR_AUTH_TOKEN_HERE", "type": "string"}, {"key": "bannerId", "value": "", "type": "string"}], "item": [{"name": "Create Banner", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Summer Sale Banner", "type": "text"}, {"key": "category", "value": "promotional", "type": "text"}, {"key": "banner", "type": "file", "src": []}]}, "url": {"raw": "{{baseUrl}}/v1/banners", "host": ["{{baseUrl}}"], "path": ["v1", "banners"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('bannerId', response.data.banner.id);", "}"]}}]}, {"name": "Get All Banners", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/banners?page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["v1", "banners"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "category", "value": "promotional", "disabled": true}]}}}, {"name": "Get Banner by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/banners/{{bannerId}}", "host": ["{{baseUrl}}"], "path": ["v1", "banners", "{{bannerId}}"]}}}, {"name": "Update Banner", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Updated Summer Sale Banner", "type": "text"}, {"key": "category", "value": "seasonal", "type": "text"}, {"key": "banner", "type": "file", "src": [], "disabled": true}]}, "url": {"raw": "{{baseUrl}}/v1/banners/{{bannerId}}", "host": ["{{baseUrl}}"], "path": ["v1", "banners", "{{bannerId}}"]}}}, {"name": "Delete Banner", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/banners/{{bannerId}}", "host": ["{{baseUrl}}"], "path": ["v1", "banners", "{{bannerId}}"]}}}, {"name": "Get Banner Categories", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/banners/categories", "host": ["{{baseUrl}}"], "path": ["v1", "banners", "categories"]}}}, {"name": "Get Banners Grouped by Category", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/banners/grouped", "host": ["{{baseUrl}}"], "path": ["v1", "banners", "grouped"]}}}, {"name": "Get Banners by Category", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/banners?category=promotional", "host": ["{{baseUrl}}"], "path": ["v1", "banners"], "query": [{"key": "category", "value": "promotional"}]}}}]}